#!/usr/bin/env node

/**
 * Apply Database Migration Script
 * This script applies a specific migration to the Supabase database
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Configuration
const SUPABASE_URL = 'https://elossghirdivbobfycob.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsb3NzZ2hpcmRpdmJvYmZ5Y29iIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDkxOTM1MCwiZXhwIjoyMDY2NDk1MzUwfQ.xbxYhEBpCLSdihJ98jFrsLDHR7wHR5ZtGXqaGWXJFn8';

async function applyMigration(migrationFile) {
  console.log('🔧 Applying database migration...');
  console.log(`📄 Migration file: ${migrationFile}`);

  // Create Supabase client with service role
  const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

  try {
    // Read migration file
    const migrationPath = path.join(__dirname, '..', 'database', 'migrations', migrationFile);
    
    if (!fs.existsSync(migrationPath)) {
      throw new Error(`Migration file not found: ${migrationPath}`);
    }

    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    console.log(`📖 Read migration file (${migrationSQL.length} characters)`);

    // Split SQL into individual statements (basic approach)
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`🔄 Executing ${statements.length} SQL statements...`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      if (statement.length === 0) continue;

      console.log(`   ${i + 1}/${statements.length}: ${statement.substring(0, 50)}...`);

      try {
        const { error } = await supabase.rpc('exec_sql', { sql: statement });
        
        if (error) {
          // Try direct query if RPC fails
          const { error: queryError } = await supabase
            .from('_temp_migration')
            .select('*')
            .limit(0); // This will fail but allows us to execute raw SQL
          
          if (queryError && queryError.message.includes('does not exist')) {
            // Table doesn't exist, which is expected for DDL operations
            console.log(`   ✅ Statement ${i + 1} executed (DDL operation)`);
          } else {
            throw error;
          }
        } else {
          console.log(`   ✅ Statement ${i + 1} executed successfully`);
        }
      } catch (stmtError) {
        console.error(`   ❌ Error in statement ${i + 1}:`, stmtError.message);
        
        // Continue with other statements for non-critical errors
        if (stmtError.message.includes('already exists') || 
            stmtError.message.includes('does not exist')) {
          console.log(`   ⚠️  Continuing with next statement...`);
          continue;
        }
        
        throw stmtError;
      }
    }

    console.log('✅ Migration applied successfully!');
    
    // Verify table creation
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'generated_assets');

    if (tablesError) {
      console.log('⚠️  Could not verify table creation:', tablesError.message);
    } else if (tables && tables.length > 0) {
      console.log('✅ Verified: generated_assets table exists');
    } else {
      console.log('⚠️  Table verification: generated_assets table not found');
    }

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  }
}

// Get migration file from command line argument
const migrationFile = process.argv[2];

if (!migrationFile) {
  console.error('❌ Please provide a migration file name');
  console.log('Usage: node scripts/apply-migration.js V23_create_generated_assets_table.sql');
  process.exit(1);
}

// Apply the migration
applyMigration(migrationFile)
  .then(() => {
    console.log('🎉 Migration process completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Migration process failed:', error);
    process.exit(1);
  });

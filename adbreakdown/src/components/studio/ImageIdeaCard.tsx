'use client';

import { useState } from 'react';
import { ImageIdea } from '@/types/studio';
import { Edit2, Check, X, Users, Target, Sparkles, Eye } from 'lucide-react';

interface ImageIdeaCardProps {
  idea: ImageIdea;
  onSelect: () => void;
  onUpdate: (updatedIdea: ImageIdea) => void;
}

const ImageIdeaCard: React.FC<ImageIdeaCardProps> = ({ idea, onSelect, onUpdate }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedIdea, setEditedIdea] = useState<ImageIdea>(idea);

  const handleSave = () => {
    onUpdate(editedIdea);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedIdea(idea);
    setIsEditing(false);
  };

  const handleArrayChange = (field: 'targetAudience' | 'USPs', value: string) => {
    const items = value.split(',').map(item => item.trim()).filter(item => item.length > 0);
    setEditedIdea(prev => ({ ...prev, [field]: items }));
  };

  if (isEditing) {
    return (
      <div className="bg-white border-2 border-blue-200 rounded-lg p-4 space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <Edit2 size={18} className="text-blue-600" />
            Edit Image Concept
          </h3>
          <div className="flex gap-2">
            <button
              onClick={handleSave}
              className="p-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              title="Save changes"
            >
              <Check size={16} />
            </button>
            <button
              onClick={handleCancel}
              className="p-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              title="Cancel editing"
            >
              <X size={16} />
            </button>
          </div>
        </div>

        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Summary</label>
            <input
              type="text"
              value={editedIdea.summary}
              onChange={(e) => setEditedIdea(prev => ({ ...prev, summary: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Visual Description</label>
            <textarea
              value={editedIdea.visualDescription}
              onChange={(e) => setEditedIdea(prev => ({ ...prev, visualDescription: e.target.value }))}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Style</label>
            <input
              type="text"
              value={editedIdea.style}
              onChange={(e) => setEditedIdea(prev => ({ ...prev, style: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Target Audience (comma-separated)</label>
            <input
              type="text"
              value={editedIdea.targetAudience.join(', ')}
              onChange={(e) => handleArrayChange('targetAudience', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Key Selling Points (comma-separated)</label>
            <input
              type="text"
              value={editedIdea.USPs.join(', ')}
              onChange={(e) => handleArrayChange('USPs', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors group">
      <div className="flex items-start justify-between mb-3">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
          <Sparkles size={18} className="text-blue-600" />
          {idea.summary}
        </h3>
        <button
          onClick={() => setIsEditing(true)}
          className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors opacity-0 group-hover:opacity-100"
          title="Edit this concept"
        >
          <Edit2 size={16} />
        </button>
      </div>

      <div className="space-y-3 mb-4">
        <div className="flex items-start gap-2">
          <Eye size={16} className="text-gray-500 mt-0.5 flex-shrink-0" />
          <div>
            <p className="text-sm font-medium text-gray-700">Visual Description</p>
            <p className="text-sm text-gray-600">{idea.visualDescription}</p>
          </div>
        </div>

        <div className="flex items-start gap-2">
          <Sparkles size={16} className="text-gray-500 mt-0.5 flex-shrink-0" />
          <div>
            <p className="text-sm font-medium text-gray-700">Style</p>
            <p className="text-sm text-gray-600">{idea.style}</p>
          </div>
        </div>

        <div className="flex items-start gap-2">
          <Users size={16} className="text-gray-500 mt-0.5 flex-shrink-0" />
          <div>
            <p className="text-sm font-medium text-gray-700">Target Audience</p>
            <div className="flex flex-wrap gap-1 mt-1">
              {idea.targetAudience.map((audience, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                >
                  {audience}
                </span>
              ))}
            </div>
          </div>
        </div>

        <div className="flex items-start gap-2">
          <Target size={16} className="text-gray-500 mt-0.5 flex-shrink-0" />
          <div>
            <p className="text-sm font-medium text-gray-700">Key Selling Points</p>
            <div className="flex flex-wrap gap-1 mt-1">
              {idea.USPs.map((usp, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full"
                >
                  {usp}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>

      <button
        onClick={onSelect}
        className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium"
      >
        Select This Concept
      </button>
    </div>
  );
};

export default ImageIdeaCard;

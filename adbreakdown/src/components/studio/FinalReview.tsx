'use client';

import { VideoIdea, Voice, AspectRatio, VideoScript } from '@/types/studio';
import { Clock, Mic, Monitor, FileText, Target, Lightbulb } from 'lucide-react';

interface FinalReviewProps {
  selectedIdea: VideoIdea;
  duration: number;
  voice: Voice;
  aspectRatio: AspectRatio;
  script: VideoScript;
  onGenerate: () => void;
}

const FinalReview: React.FC<FinalReviewProps> = ({ 
  selectedIdea, 
  duration, 
  voice, 
  aspectRatio, 
  script, 
  onGenerate 
}) => {

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 max-w-2xl">
      <div className="mb-6">
        <h2 className="text-xl font-bold text-gray-900 mb-2">
          Final Review
        </h2>
        <p className="text-gray-600">
          Review your video ad configuration before generation
        </p>
      </div>

      <div className="space-y-6">
        {/* Selected Idea */}
        {selectedIdea && (
          <div className="border-b border-gray-200 pb-4">
            <div className="flex items-center gap-2 mb-3">
              <Lightbulb className="text-purple-600" size={20} />
              <h3 className="font-semibold text-gray-900">Creative Concept</h3>
            </div>
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-sm text-gray-700 mb-2">
                <span className="font-medium">Summary:</span> {selectedIdea.summary}
              </p>
              <p className="text-sm text-gray-700 mb-2">
                <span className="font-medium">Hook:</span> {selectedIdea.hook}
              </p>
              <div className="flex flex-wrap gap-1 mt-2">
                {selectedIdea.targetAudience.map((audience, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full"
                  >
                    {audience}
                  </span>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Video Parameters */}
        <div className="border-b border-gray-200 pb-4">
          <div className="flex items-center gap-2 mb-3">
            <FileText className="text-blue-600" size={20} />
            <h3 className="font-semibold text-gray-900">Video Settings</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-2">
              <Clock className="text-gray-400" size={16} />
              <span className="text-sm text-gray-700">
                <span className="font-medium">Duration:</span> {duration}s
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Mic className="text-gray-400" size={16} />
              <span className="text-sm text-gray-700">
                <span className="font-medium">Voice:</span> {voice?.name || 'Not selected'}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Monitor className="text-gray-400" size={16} />
              <span className="text-sm text-gray-700">
                <span className="font-medium">Format:</span> {aspectRatio}
              </span>
            </div>
          </div>
        </div>

        {/* Script Preview */}
        {script && (
          <div className="border-b border-gray-200 pb-4">
            <div className="flex items-center gap-2 mb-3">
              <Target className="text-green-600" size={20} />
              <h3 className="font-semibold text-gray-900">Script Overview</h3>
            </div>
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-sm text-gray-700 mb-2">
                <span className="font-medium">Scenes:</span> {script.scenes.length}
              </p>
              <div className="space-y-2">
                {script.scenes.slice(0, 2).map((scene, index) => (
                  <div key={index} className="text-xs text-gray-600">
                    <span className="font-medium">Scene {scene.sceneNumber}:</span> 
                    {scene.voiceoverText.substring(0, 60)}...
                  </div>
                ))}
                {script.scenes.length > 2 && (
                  <p className="text-xs text-gray-500">
                    +{script.scenes.length - 2} more scenes
                  </p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Generation Status */}
        <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-2">Ready to Generate</h4>
          <p className="text-sm text-gray-600 mb-4">
            Your AI video ad will be generated using the settings above. 
            This process typically takes 2-5 minutes.
          </p>
          
          <button
            onClick={onGenerate}
            className="w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white py-3 px-4 rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all font-medium shadow-sm"
          >
            🚀 Generate AI Video Ad
          </button>
        </div>
      </div>
    </div>
  );
};

export default FinalReview;
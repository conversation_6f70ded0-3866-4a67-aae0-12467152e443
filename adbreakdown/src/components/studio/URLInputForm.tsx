'use client';

import { useState } from 'react';
import ManualInfoForm, { ManualInfoData } from './ManualInfoForm';

interface URLInputFormProps {
  onSubmit: (url: string) => void;
  onManualSubmit?: (data: ManualInfoData) => void;
}

const URLInputForm: React.FC<URLInputFormProps> = ({ onSubmit, onManualSubmit }) => {
  const [url, setUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showManualForm, setShowManualForm] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!url.trim()) return;

    const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
    if (!urlPattern.test(url)) {
      alert('Please enter a valid URL');
      return;
    }

    setIsLoading(true);
    const formattedUrl = url.startsWith('http') ? url : `https://${url}`;
    
    try {
      await onSubmit(formattedUrl);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSkip = () => {
    setShowManualForm(true);
  };

  const handleManualSubmit = async (data: ManualInfoData) => {
    if (onManualSubmit) {
      await onManualSubmit(data);
    }
  };

  const handleCancelManual = () => {
    setShowManualForm(false);
  };

  if (showManualForm) {
    return (
      <ManualInfoForm
        onSubmit={handleManualSubmit}
        onCancel={handleCancelManual}
      />
    );
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 max-w-md">
      <div className="mb-3">
        <label className="block text-sm font-medium text-gray-900 mb-2">
          Enter product url
        </label>
        <input
          type="text"
          value={url}
          onChange={(e) => setUrl(e.target.value)}
          placeholder="https://example.com"
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          disabled={isLoading}
        />
      </div>
      
      <div className="flex gap-2">
        <button
          onClick={handleSubmit}
          disabled={!url.trim() || isLoading}
          className="flex-1 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
        >
          {isLoading ? 'Analyzing...' : 'Submit'}
        </button>
        
        <button
          onClick={handleSkip}
          disabled={isLoading}
          className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
        >
          Skip
        </button>
      </div>
    </div>
  );
};

export default URLInputForm;
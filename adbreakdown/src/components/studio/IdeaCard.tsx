'use client';

import { useState } from 'react';
import { VideoIdea } from '@/types/studio';
import { Edit3, Save, X, Plus, Trash2 } from 'lucide-react';

interface IdeaCardProps {
  idea: VideoIdea;
  onSelect: () => void;
  onUpdate?: (updatedIdea: VideoIdea) => void;
}

const IdeaCard: React.FC<IdeaCardProps> = ({ idea, onSelect, onUpdate }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedIdea, setEditedIdea] = useState<VideoIdea>(idea);
  const [newAudience, setNewAudience] = useState('');
  const [newUSP, setNewUSP] = useState('');

  const handleSave = () => {
    if (onUpdate) {
      onUpdate(editedIdea);
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedIdea(idea);
    setNewAudience('');
    setNewUSP('');
    setIsEditing(false);
  };

  const handleAddAudience = () => {
    if (newAudience.trim()) {
      setEditedIdea(prev => ({
        ...prev,
        targetAudience: [...prev.targetAudience, newAudience.trim()]
      }));
      setNewAudience('');
    }
  };

  const handleRemoveAudience = (index: number) => {
    setEditedIdea(prev => ({
      ...prev,
      targetAudience: prev.targetAudience.filter((_, i) => i !== index)
    }));
  };

  const handleAddUSP = () => {
    if (newUSP.trim()) {
      setEditedIdea(prev => ({
        ...prev,
        USPs: [...prev.USPs, newUSP.trim()]
      }));
      setNewUSP('');
    }
  };

  const handleRemoveUSP = (index: number) => {
    setEditedIdea(prev => ({
      ...prev,
      USPs: prev.USPs.filter((_, i) => i !== index)
    }));
  };

  return (
    <div className="bg-gray-900 rounded-lg p-6 shadow-lg border border-gray-700 max-w-2xl">
      {/* Header with edit button */}
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-lg font-semibold text-gray-100">Video Idea</h2>
        {onUpdate && (
          <div className="flex gap-2">
            {isEditing ? (
              <>
                <button
                  onClick={handleSave}
                  className="p-2 text-green-400 hover:text-green-300 transition-colors"
                  title="Save changes"
                >
                  <Save size={16} />
                </button>
                <button
                  onClick={handleCancel}
                  className="p-2 text-red-400 hover:text-red-300 transition-colors"
                  title="Cancel editing"
                >
                  <X size={16} />
                </button>
              </>
            ) : (
              <button
                onClick={() => setIsEditing(true)}
                className="p-2 text-gray-400 hover:text-purple-400 transition-colors"
                title="Edit idea"
              >
                <Edit3 size={16} />
              </button>
            )}
          </div>
        )}
      </div>

      <div className="space-y-6">
        <div>
          <h3 className="text-xs font-bold text-gray-400 uppercase tracking-wide mb-2">
            SUMMARY
          </h3>
          {isEditing ? (
            <textarea
              value={editedIdea.summary}
              onChange={(e) => setEditedIdea(prev => ({ ...prev, summary: e.target.value }))}
              className="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-gray-100 text-sm leading-relaxed focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
              rows={3}
            />
          ) : (
            <p className="text-gray-100 text-sm leading-relaxed">
              {editedIdea.summary}
            </p>
          )}
        </div>

        <div className="border-t border-gray-700"></div>

        <div>
          <h3 className="text-xs font-bold text-gray-400 uppercase tracking-wide mb-2">
            HOOK
          </h3>
          {isEditing ? (
            <textarea
              value={editedIdea.hook}
              onChange={(e) => setEditedIdea(prev => ({ ...prev, hook: e.target.value }))}
              className="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-gray-100 text-sm font-medium focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
              rows={2}
            />
          ) : (
            <p className="text-gray-100 text-sm font-medium">
              {editedIdea.hook}
            </p>
          )}
        </div>

        <div className="border-t border-gray-700"></div>

        <div>
          <h3 className="text-xs font-bold text-gray-400 uppercase tracking-wide mb-2">
            TARGET AUDIENCE
          </h3>
          <div className="flex flex-wrap gap-2 mb-2">
            {editedIdea.targetAudience.map((audience, index) => (
              <span
                key={index}
                className="px-3 py-1 bg-gray-700 text-gray-200 text-xs rounded-full flex items-center gap-1"
              >
                {audience}
                {isEditing && (
                  <button
                    onClick={() => handleRemoveAudience(index)}
                    className="text-red-400 hover:text-red-300 ml-1"
                  >
                    <X size={12} />
                  </button>
                )}
              </span>
            ))}
          </div>
          {isEditing && (
            <div className="flex gap-2">
              <input
                type="text"
                value={newAudience}
                onChange={(e) => setNewAudience(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleAddAudience()}
                placeholder="Add audience segment"
                className="flex-1 p-2 bg-gray-800 border border-gray-600 rounded text-gray-100 text-xs focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
              <button
                onClick={handleAddAudience}
                className="p-2 text-purple-400 hover:text-purple-300 transition-colors"
              >
                <Plus size={16} />
              </button>
            </div>
          )}
        </div>

        <div className="border-t border-gray-700"></div>

        <div>
          <h3 className="text-xs font-bold text-gray-400 uppercase tracking-wide mb-2">
            USPS
          </h3>
          <ul className="space-y-1 mb-2">
            {editedIdea.USPs.map((usp, index) => (
              <li key={index} className="text-gray-100 text-sm flex items-start">
                <span className="text-purple-400 mr-2">•</span>
                <span className="flex-1">{usp}</span>
                {isEditing && (
                  <button
                    onClick={() => handleRemoveUSP(index)}
                    className="text-red-400 hover:text-red-300 ml-2"
                  >
                    <Trash2 size={12} />
                  </button>
                )}
              </li>
            ))}
          </ul>
          {isEditing && (
            <div className="flex gap-2">
              <input
                type="text"
                value={newUSP}
                onChange={(e) => setNewUSP(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleAddUSP()}
                placeholder="Add unique selling point"
                className="flex-1 p-2 bg-gray-800 border border-gray-600 rounded text-gray-100 text-xs focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
              <button
                onClick={handleAddUSP}
                className="p-2 text-purple-400 hover:text-purple-300 transition-colors"
              >
                <Plus size={16} />
              </button>
            </div>
          )}
        </div>
      </div>

      <div className="mt-6 pt-4 border-t border-gray-700">
        <button
          onClick={onSelect}
          className="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
        >
          Use Idea
        </button>
      </div>
    </div>
  );
};

export default IdeaCard;
'use client';

import { useState } from 'react';
import { Edit3, Trash2, Play, ChevronDown } from 'lucide-react';
import { VideoScript, Voice } from '@/types/studio';

interface ScriptEditorProps {
  script: VideoScript;
  onUpdate: (script: VideoScript) => void;
  onNext: () => void;
}

const ScriptEditor: React.FC<ScriptEditorProps> = ({ script, onUpdate, onNext }) => {
  const [editingScene, setEditingScene] = useState<number | null>(null);
  const [selectedVoice, setSelectedVoice] = useState<Voice | null>(null);
  const [isVoiceDropdownOpen, setIsVoiceDropdownOpen] = useState(false);

  const mockVoices: Voice[] = [
    { id: 'voice_1', name: '<PERSON> (Professional)', gender: 'female', accent: 'American' },
    { id: 'voice_2', name: '<PERSON> (<PERSON>)', gender: 'male', accent: 'British' },
    { id: 'voice_3', name: '<PERSON> (Energetic)', gender: 'female', accent: 'Spanish' },
  ];

  const handleSceneEdit = (sceneIndex: number, field: 'visualDescription' | 'voiceoverText', value: string) => {
    const updatedScript = {
      ...script,
      scenes: script.scenes.map((scene, index) =>
        index === sceneIndex ? { ...scene, [field]: value } : scene
      ),
    };
    onUpdate(updatedScript);
  };

  const handleDeleteScene = (sceneIndex: number) => {
    const updatedScript = {
      ...script,
      scenes: script.scenes.filter((_, index) => index !== sceneIndex),
    };
    // Re-number scenes
    updatedScript.scenes = updatedScript.scenes.map((scene, index) => ({
      ...scene,
      sceneNumber: index + 1,
    }));
    onUpdate(updatedScript);
  };

  const extractKeywords = () => {
    const text = script.scenes.map(scene => 
      scene.voiceoverText + ' ' + scene.visualDescription
    ).join(' ');
    
    // Extract meaningful keywords from the script content
    const words = text.toLowerCase().split(/\s+/);
    const stopWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'from', 'up', 'about', 'into', 'over', 'after', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'shall', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'we', 'they', 'it', 'your', 'our', 'their']);
    
    // Count word frequency
    const wordCount = new Map<string, number>();
    words.forEach(word => {
      const cleanWord = word.replace(/[^\w]/g, '');
      if (cleanWord.length > 2 && !stopWords.has(cleanWord) && isNaN(Number(cleanWord))) {
        wordCount.set(cleanWord, (wordCount.get(cleanWord) || 0) + 1);
      }
    });
    
    // Get top keywords, prioritizing longer words and higher frequency
    const keywords = Array.from(wordCount.entries())
      .sort((a, b) => {
        // Sort by frequency first, then by word length
        if (b[1] !== a[1]) return b[1] - a[1];
        return b[0].length - a[0].length;
      })
      .slice(0, 4)
      .map(([word]) => 
        // Capitalize first letter
        word.charAt(0).toUpperCase() + word.slice(1)
      );
    
    // If we don't have enough keywords, add some generic ones
    if (keywords.length < 2) {
      keywords.push('Commercial', 'Advertisement');
    }
    
    return keywords.slice(0, 4);
  };

  return (
    <div className="bg-gray-50 rounded-lg p-6 max-w-4xl">
      {/* Title
      <div className="mb-6">
        <h2 className="text-xl font-bold text-gray-900 mb-2">
          (need to write logic to generate title from idea)
        </h2>
      </div>
      */}

      <div className="space-y-4 mb-6">
        {script.scenes.map((scene, index) => (
          <div
            key={scene.sceneNumber}
            className="bg-white rounded-lg p-4 border border-gray-200 group hover:border-gray-300 transition-colors"
          >
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold text-gray-900">Scene {scene.sceneNumber}</h3>
              <div className="flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <button
                  onClick={() => setEditingScene(editingScene === index ? null : index)}
                  className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                >
                  <Edit3 size={16} />
                </button>
                <button
                  onClick={() => handleDeleteScene(index)}
                  className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                >
                  <Trash2 size={16} />
                </button>
              </div>
            </div>

            <div className="space-y-3">
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-1">Visuals</h4>
                {editingScene === index ? (
                  <textarea
                    value={scene.visualDescription}
                    onChange={(e) => handleSceneEdit(index, 'visualDescription', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
                    rows={2}
                  />
                ) : (
                  <p className="text-sm text-gray-600">{scene.visualDescription}</p>
                )}
              </div>

              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-1">Voiceover</h4>
                {editingScene === index ? (
                  <textarea
                    value={scene.voiceoverText}
                    onChange={(e) => handleSceneEdit(index, 'voiceoverText', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
                    rows={2}
                  />
                ) : (
                  <p className="text-sm text-gray-600">{scene.voiceoverText}</p>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Keywords */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-gray-700 mb-2">Keywords</h4>
        <div className="flex flex-wrap gap-2">
          {extractKeywords().map((keyword, index) => (
            <span
              key={index}
              className="px-3 py-1 bg-gray-200 text-gray-700 text-sm rounded-full"
            >
              {keyword}
            </span>
          ))}
        </div>
      </div>

      {/* Voice Selection */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-gray-700 mb-2">Voice</h4>
        <div className="flex items-center gap-3">
          <div className="relative">
            <button
              onClick={() => setIsVoiceDropdownOpen(!isVoiceDropdownOpen)}
              className="flex items-center gap-2 px-3 py-2 bg-white border border-gray-300 rounded-lg hover:border-gray-400 transition-colors"
            >
              <span className="text-sm">
                {selectedVoice ? selectedVoice.name : 'Select Voice'}
              </span>
              <ChevronDown size={16} />
            </button>
            
            {isVoiceDropdownOpen && (
              <div className="absolute top-full left-0 mt-1 w-64 bg-white border border-gray-300 rounded-lg shadow-lg z-10">
                {mockVoices.map((voice) => (
                  <button
                    key={voice.id}
                    onClick={() => {
                      setSelectedVoice(voice);
                      setIsVoiceDropdownOpen(false);
                    }}
                    className="w-full flex items-center justify-between p-3 hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg"
                  >
                    <div className="text-left">
                      <p className="text-sm font-medium text-gray-900">{voice.name}</p>
                      <p className="text-xs text-gray-500">{voice.accent} • {voice.gender}</p>
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>
          
          {selectedVoice && (
            <button
              onClick={() => {
                // Play voice preview
              }}
              className="p-2 text-gray-400 hover:text-purple-600 transition-colors"
            >
              <Play size={16} />
            </button>
          )}
        </div>
      </div>

      {/* Action Button */}
      <div className="pt-4 border-t border-gray-200">
        <button
          onClick={onNext}
          className="w-full bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700 transition-colors font-medium"
        >
          Use Script
        </button>
      </div>
    </div>
  );
};

export default ScriptEditor;
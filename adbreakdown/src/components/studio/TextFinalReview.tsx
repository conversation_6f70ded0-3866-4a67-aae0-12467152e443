'use client';

import { ContentType, Platform, ToneOfVoice, ContentLength } from '@/types/studio';
import { 
  FileText, 
  Tv, 
  Instagram, 
  Facebook, 
  Youtube, 
  Search, 
  Play,
  Eye,
  Clock,
  MessageSquare,
  Target
} from 'lucide-react';

interface TextFinalReviewProps {
  contentType: ContentType;
  platform: Platform;
  tone: ToneOfVoice;
  length: ContentLength;
  onGenerate: () => void;
}

const TextFinalReview: React.FC<TextFinalReviewProps> = ({
  contentType,
  platform,
  tone,
  length,
  onGenerate
}) => {
  const getContentTypeDisplay = (type: ContentType) => {
    switch (type) {
      case 'tv-ad-30s':
        return { name: '30s TV Commercial', icon: '📺', description: 'Traditional television advertisement script' };
      case 'reel-30s':
        return { name: '30s Social Reel', icon: '📱', description: 'Instagram/TikTok reel script' };
      case 'bumper-5s':
        return { name: '5s Bumper Ad', icon: '⚡', description: 'Short, punchy bumper advertisement' };
      case 'performance-ad':
        return { name: 'Performance Ad Copy', icon: '🎯', description: 'Conversion-focused ad copy' };
      case 'google-ad':
        return { name: 'Google Ads Copy', icon: '🔍', description: 'Search and display ad copy' };
      case 'meta-ad':
        return { name: 'Meta Ads Copy', icon: '📘', description: 'Facebook and Instagram ad copy' };
      default:
        return { name: type, icon: '📄', description: 'Custom content type' };
    }
  };

  const getPlatformDisplay = (plat: Platform) => {
    switch (plat) {
      case 'tv':
        return { name: 'Television', icon: Tv };
      case 'instagram':
        return { name: 'Instagram', icon: Instagram };
      case 'facebook':
        return { name: 'Facebook', icon: Facebook };
      case 'youtube':
        return { name: 'YouTube', icon: Youtube };
      case 'google':
        return { name: 'Google Ads', icon: Search };
      case 'meta':
        return { name: 'Meta Platforms', icon: Facebook };
      default:
        return { name: plat, icon: FileText };
    }
  };

  const contentTypeDisplay = getContentTypeDisplay(contentType);
  const platformDisplay = getPlatformDisplay(platform);
  const PlatformIcon = platformDisplay.icon;

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 max-w-4xl">
      <div className="flex items-center gap-2 mb-6">
        <Eye className="w-5 h-5 text-green-600" />
        <h3 className="text-lg font-semibold text-gray-900">Final Review</h3>
      </div>

      <div className="space-y-6">
        {/* Content Type Summary */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-3">
            <FileText className="w-4 h-4 text-green-600" />
            <h4 className="font-medium text-green-900">Content Type</h4>
          </div>
          <div className="flex items-center gap-3">
            <span className="text-2xl">{contentTypeDisplay.icon}</span>
            <div>
              <p className="text-sm font-medium text-green-800">{contentTypeDisplay.name}</p>
              <p className="text-xs text-green-600">{contentTypeDisplay.description}</p>
            </div>
          </div>
        </div>

        {/* Configuration Grid */}
        <div className="grid md:grid-cols-3 gap-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <PlatformIcon className="w-4 h-4 text-blue-600" />
              <h4 className="font-medium text-blue-900">Platform</h4>
            </div>
            <p className="text-sm text-blue-800 capitalize">{platformDisplay.name}</p>
          </div>

          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <MessageSquare className="w-4 h-4 text-purple-600" />
              <h4 className="font-medium text-purple-900">Tone</h4>
            </div>
            <p className="text-sm text-purple-800 capitalize">{tone.replace('-', ' ')}</p>
          </div>

          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="w-4 h-4 text-orange-600" />
              <h4 className="font-medium text-orange-900">Length</h4>
            </div>
            <p className="text-sm text-orange-800 capitalize">{length.replace('-', ' ')}</p>
          </div>
        </div>

        {/* Generation Details */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <Target className="w-4 h-4 text-yellow-600" />
            <h4 className="font-medium text-yellow-900">What You'll Get</h4>
          </div>
          <ul className="text-sm text-yellow-800 space-y-1">
            <li>• AI-generated content optimized for {platformDisplay.name}</li>
            <li>• {tone.charAt(0).toUpperCase() + tone.slice(1)} tone of voice throughout</li>
            <li>• {length.includes('s') ? `${length} duration script` : `${length} format copy`}</li>
            <li>• Ready-to-use content with clear call-to-action</li>
            <li>• Editable text format for easy customization</li>
          </ul>
        </div>

        {/* Generate Button */}
        <div className="flex justify-center pt-4">
          <button
            onClick={onGenerate}
            className="px-8 py-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg hover:from-green-700 hover:to-emerald-700 transition-all font-medium text-lg flex items-center gap-3 shadow-lg hover:shadow-xl transform hover:scale-105"
          >
            <Play size={20} />
            Generate My Content
          </button>
        </div>

        {/* Disclaimer */}
        <div className="text-center">
          <p className="text-xs text-gray-500">
            Generated content will be optimized for your selected platform and can be further customized as needed.
          </p>
        </div>
      </div>
    </div>
  );
};

export default TextFinalReview;

'use client';

import { useState, useEffect } from 'react';
import { Play, Volume2 } from 'lucide-react';
import { Voice, AspectRatio } from '@/types/studio';

interface ParameterSelectorsProps {
  type: 'duration' | 'voice' | 'aspectRatio';
  onSelect: (value: any) => void;
}

const ParameterSelectors: React.FC<ParameterSelectorsProps> = ({ type, onSelect }) => {
  const [selectedValue, setSelectedValue] = useState<any>(null);
  const [voices, setVoices] = useState<Voice[]>([]);

  useEffect(() => {
    if (type === 'voice') {
      fetchVoices();
    }
  }, [type]);

  const fetchVoices = async () => {
    // Mock voices for now - replace with actual API call
    const mockVoices: Voice[] = [
      { id: 'voice_1', name: '<PERSON> (Professional)', gender: 'female', accent: 'American' },
      { id: 'voice_2', name: '<PERSON> (<PERSON>)', gender: 'male', accent: 'British' },
      { id: 'voice_3', name: '<PERSON> (Energetic)', gender: 'female', accent: 'Spanish' },
      { id: 'voice_4', name: '<PERSON> (Authoritative)', gender: 'male', accent: 'Australian' },
    ];
    setVoices(mockVoices);
  };

  const handleSelect = (value: any) => {
    setSelectedValue(value);
  };

  const handleSubmit = () => {
    if (selectedValue) {
      onSelect(selectedValue);
    }
  };

  if (type === 'duration') {
    const durations = [10, 15, 20];
    
    return (
      <div className="bg-white border border-gray-200 rounded-lg p-4 max-w-md">
        <div className="mb-4">
          <h3 className="text-sm font-medium text-gray-900 mb-3">Select Duration</h3>
          <div className="flex gap-2">
            {durations.map((duration) => (
              <button
                key={duration}
                onClick={() => handleSelect(duration)}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  selectedValue === duration
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {duration} sec
              </button>
            ))}
          </div>
        </div>
        
        {selectedValue && (
          <button
            onClick={handleSubmit}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors"
          >
            Submit
          </button>
        )}
      </div>
    );
  }

  if (type === 'voice') {
    return (
      <div className="bg-white border border-gray-200 rounded-lg p-4 max-w-md">
        <div className="mb-4">
          <h3 className="text-sm font-medium text-gray-900 mb-3">Select Voice</h3>
          <div className="space-y-2">
            {voices.map((voice) => (
              <div
                key={voice.id}
                className={`flex items-center justify-between p-3 rounded-lg border cursor-pointer transition-colors ${
                  selectedValue?.id === voice.id
                    ? 'border-purple-600 bg-purple-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => handleSelect(voice)}
              >
                <div>
                  <p className="font-medium text-gray-900">{voice.name}</p>
                  <p className="text-sm text-gray-500">{voice.accent} • {voice.gender}</p>
                </div>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    // Play voice preview
                  }}
                  className="p-2 text-gray-400 hover:text-purple-600 transition-colors"
                >
                  <Play size={16} />
                </button>
              </div>
            ))}
          </div>
        </div>
        
        {selectedValue && (
          <button
            onClick={handleSubmit}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors"
          >
            Submit
          </button>
        )}
      </div>
    );
  }

  if (type === 'aspectRatio') {
    const aspectRatios: { value: AspectRatio; label: string; description: string }[] = [
      { value: '9:16', label: '9:16', description: 'Instagram Reels, TikTok' },
      { value: '16:9', label: '16:9', description: 'YouTube, Facebook' },
      { value: '1:1', label: '1:1', description: 'Instagram Square' },
    ];
    
    return (
      <div className="bg-white border border-gray-200 rounded-lg p-4 max-w-md">
        <div className="mb-4">
          <h3 className="text-sm font-medium text-gray-900 mb-3">Select Format</h3>
          <div className="space-y-2">
            {aspectRatios.map((ratio) => (
              <button
                key={ratio.value}
                onClick={() => handleSelect(ratio.value)}
                className={`w-full flex items-center justify-between p-3 rounded-lg border text-left transition-colors ${
                  selectedValue === ratio.value
                    ? 'border-purple-600 bg-purple-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div>
                  <p className="font-medium text-gray-900">{ratio.label}</p>
                  <p className="text-sm text-gray-500">{ratio.description}</p>
                </div>
              </button>
            ))}
          </div>
        </div>
        
        {selectedValue && (
          <button
            onClick={handleSubmit}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors"
          >
            Submit
          </button>
        )}
      </div>
    );
  }

  return null;
};

export default ParameterSelectors;
'use client';

import { useState } from 'react';
import { ContentType, Platform, ToneOfVoice, ContentLength } from '@/types/studio';
import { Tv, Instagram, Facebook, Youtube, Search, ArrowRight } from 'lucide-react';

interface TextCustomizationFormProps {
  contentType: ContentType;
  onSubmit: (platform: Platform, tone: ToneOfVoice, length: ContentLength) => void;
}

const TextCustomizationForm: React.FC<TextCustomizationFormProps> = ({ contentType, onSubmit }) => {
  const [selectedPlatform, setSelectedPlatform] = useState<Platform | null>(null);
  const [selectedTone, setSelectedTone] = useState<ToneOfVoice | null>(null);
  const [selectedLength, setSelectedLength] = useState<ContentLength | null>(null);

  const platforms = [
    { id: 'tv' as Platform, name: 'Television', icon: Tv, description: 'Traditional TV broadcast' },
    { id: 'instagram' as Platform, name: 'Instagram', icon: Instagram, description: 'Stories, Reels, Feed posts' },
    { id: 'facebook' as Platform, name: 'Facebook', icon: Facebook, description: 'News feed, Stories, Video ads' },
    { id: 'youtube' as Platform, name: 'YouTube', icon: Youtube, description: 'Pre-roll, Mid-roll, Bumper ads' },
    { id: 'google' as Platform, name: 'Google Ads', icon: Search, description: 'Search, Display, Shopping ads' },
    { id: 'meta' as Platform, name: 'Meta Platforms', icon: Facebook, description: 'Facebook & Instagram combined' }
  ];

  const tones = [
    { id: 'professional' as ToneOfVoice, name: 'Professional', description: 'Formal, authoritative, business-focused' },
    { id: 'casual' as ToneOfVoice, name: 'Casual', description: 'Relaxed, approachable, everyday language' },
    { id: 'friendly' as ToneOfVoice, name: 'Friendly', description: 'Warm, welcoming, personable' },
    { id: 'urgent' as ToneOfVoice, name: 'Urgent', description: 'Time-sensitive, action-oriented, compelling' },
    { id: 'humorous' as ToneOfVoice, name: 'Humorous', description: 'Light-hearted, entertaining, memorable' },
    { id: 'authoritative' as ToneOfVoice, name: 'Authoritative', description: 'Expert, confident, trustworthy' },
    { id: 'emotional' as ToneOfVoice, name: 'Emotional', description: 'Heart-felt, inspiring, moving' },
    { id: 'conversational' as ToneOfVoice, name: 'Conversational', description: 'Natural, dialogue-like, engaging' }
  ];

  const lengths = [
    { id: '5s' as ContentLength, name: '5 seconds', description: 'Quick, punchy message' },
    { id: '15s' as ContentLength, name: '15 seconds', description: 'Brief but complete story' },
    { id: '30s' as ContentLength, name: '30 seconds', description: 'Standard commercial length' },
    { id: '60s' as ContentLength, name: '60 seconds', description: 'Extended narrative format' },
    { id: 'short' as ContentLength, name: 'Short Copy', description: 'Headlines and brief descriptions' },
    { id: 'medium' as ContentLength, name: 'Medium Copy', description: 'Detailed product descriptions' },
    { id: 'long' as ContentLength, name: 'Long Copy', description: 'Comprehensive sales copy' }
  ];

  // Filter options based on content type
  const getRelevantPlatforms = () => {
    switch (contentType) {
      case 'tv-ad-30s':
        return platforms.filter(p => ['tv', 'youtube'].includes(p.id));
      case 'reel-30s':
        return platforms.filter(p => ['instagram', 'facebook', 'meta'].includes(p.id));
      case 'bumper-5s':
        return platforms.filter(p => ['youtube', 'instagram', 'facebook'].includes(p.id));
      case 'google-ad':
        return platforms.filter(p => p.id === 'google');
      case 'meta-ad':
        return platforms.filter(p => ['facebook', 'instagram', 'meta'].includes(p.id));
      default:
        return platforms;
    }
  };

  const getRelevantLengths = () => {
    switch (contentType) {
      case 'tv-ad-30s':
      case 'reel-30s':
        return lengths.filter(l => ['15s', '30s', '60s'].includes(l.id));
      case 'bumper-5s':
        return lengths.filter(l => l.id === '5s');
      case 'google-ad':
      case 'meta-ad':
        return lengths.filter(l => ['short', 'medium', 'long'].includes(l.id));
      default:
        return lengths;
    }
  };

  const handleSubmit = () => {
    if (selectedPlatform && selectedTone && selectedLength) {
      onSubmit(selectedPlatform, selectedTone, selectedLength);
    }
  };

  const relevantPlatforms = getRelevantPlatforms();
  const relevantLengths = getRelevantLengths();

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 max-w-4xl space-y-6">
      {/* Platform Selection */}
      <div>
        <h4 className="text-md font-semibold text-gray-900 mb-3">Select Platform</h4>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {relevantPlatforms.map((platform) => {
            const IconComponent = platform.icon;
            return (
              <button
                key={platform.id}
                onClick={() => setSelectedPlatform(platform.id)}
                className={`p-3 rounded-lg border-2 transition-all text-left ${
                  selectedPlatform === platform.id
                    ? 'border-green-500 bg-green-50'
                    : 'border-gray-200 bg-white hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2 mb-1">
                  <IconComponent size={16} className={selectedPlatform === platform.id ? 'text-green-600' : 'text-gray-600'} />
                  <span className={`text-sm font-medium ${selectedPlatform === platform.id ? 'text-green-900' : 'text-gray-900'}`}>
                    {platform.name}
                  </span>
                </div>
                <p className={`text-xs ${selectedPlatform === platform.id ? 'text-green-700' : 'text-gray-600'}`}>
                  {platform.description}
                </p>
              </button>
            );
          })}
        </div>
      </div>

      {/* Tone Selection */}
      <div>
        <h4 className="text-md font-semibold text-gray-900 mb-3">Choose Tone of Voice</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {tones.map((tone) => (
            <button
              key={tone.id}
              onClick={() => setSelectedTone(tone.id)}
              className={`p-3 rounded-lg border-2 transition-all text-left ${
                selectedTone === tone.id
                  ? 'border-green-500 bg-green-50'
                  : 'border-gray-200 bg-white hover:border-gray-300'
              }`}
            >
              <span className={`text-sm font-medium block mb-1 ${selectedTone === tone.id ? 'text-green-900' : 'text-gray-900'}`}>
                {tone.name}
              </span>
              <p className={`text-xs ${selectedTone === tone.id ? 'text-green-700' : 'text-gray-600'}`}>
                {tone.description}
              </p>
            </button>
          ))}
        </div>
      </div>

      {/* Length Selection */}
      <div>
        <h4 className="text-md font-semibold text-gray-900 mb-3">Content Length</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {relevantLengths.map((length) => (
            <button
              key={length.id}
              onClick={() => setSelectedLength(length.id)}
              className={`p-3 rounded-lg border-2 transition-all text-left ${
                selectedLength === length.id
                  ? 'border-green-500 bg-green-50'
                  : 'border-gray-200 bg-white hover:border-gray-300'
              }`}
            >
              <span className={`text-sm font-medium block mb-1 ${selectedLength === length.id ? 'text-green-900' : 'text-gray-900'}`}>
                {length.name}
              </span>
              <p className={`text-xs ${selectedLength === length.id ? 'text-green-700' : 'text-gray-600'}`}>
                {length.description}
              </p>
            </button>
          ))}
        </div>
      </div>

      {/* Submit Button */}
      {selectedPlatform && selectedTone && selectedLength && (
        <div className="flex justify-center pt-4">
          <button
            onClick={handleSubmit}
            className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium flex items-center gap-2"
          >
            Continue to Review
            <ArrowRight size={16} />
          </button>
        </div>
      )}

      {/* Selection Summary */}
      {(selectedPlatform || selectedTone || selectedLength) && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h5 className="text-sm font-medium text-gray-900 mb-2">Current Selection:</h5>
          <div className="flex flex-wrap gap-2">
            {selectedPlatform && (
              <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                Platform: {relevantPlatforms.find(p => p.id === selectedPlatform)?.name}
              </span>
            )}
            {selectedTone && (
              <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                Tone: {tones.find(t => t.id === selectedTone)?.name}
              </span>
            )}
            {selectedLength && (
              <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
                Length: {relevantLengths.find(l => l.id === selectedLength)?.name}
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default TextCustomizationForm;

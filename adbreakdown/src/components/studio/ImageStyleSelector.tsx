'use client';

import { useState } from 'react';
import { AspectRatio } from '@/types/studio';
import { Palette, Monitor, Smartphone, Square } from 'lucide-react';

interface ImageStyleSelectorProps {
  onStyleSelect: (style: string) => void;
  onAspectRatioSelect: (aspectRatio: AspectRatio) => void;
}

const ImageStyleSelector: React.FC<ImageStyleSelectorProps> = ({ 
  onStyleSelect, 
  onAspectRatioSelect 
}) => {
  const [selectedStyle, setSelectedStyle] = useState<string | null>(null);
  const [selectedAspectRatio, setSelectedAspectRatio] = useState<AspectRatio | null>(null);

  const styles = [
    {
      id: 'photorealistic',
      name: 'Photorealistic',
      description: 'High-quality, realistic photography style',
      preview: '📸'
    },
    {
      id: 'minimalist',
      name: 'Minimalist',
      description: 'Clean, simple design with lots of white space',
      preview: '⚪'
    },
    {
      id: 'modern',
      name: 'Modern',
      description: 'Contemporary design with bold colors and shapes',
      preview: '🎨'
    },
    {
      id: 'vintage',
      name: 'Vintage',
      description: 'Retro aesthetic with warm tones and classic elements',
      preview: '📻'
    },
    {
      id: 'corporate',
      name: 'Corporate',
      description: 'Professional business style with clean lines',
      preview: '🏢'
    },
    {
      id: 'artistic',
      name: 'Artistic',
      description: 'Creative and expressive with artistic flair',
      preview: '🎭'
    }
  ];

  const aspectRatios = [
    {
      ratio: '1:1' as AspectRatio,
      name: 'Square',
      description: 'Perfect for Instagram posts and social media',
      icon: Square,
      dimensions: '1080x1080'
    },
    {
      ratio: '16:9' as AspectRatio,
      name: 'Landscape',
      description: 'Great for websites and desktop displays',
      icon: Monitor,
      dimensions: '1920x1080'
    },
    {
      ratio: '9:16' as AspectRatio,
      name: 'Portrait',
      description: 'Ideal for mobile screens and stories',
      icon: Smartphone,
      dimensions: '1080x1920'
    }
  ];

  const handleStyleSelect = (style: string) => {
    setSelectedStyle(style);
    onStyleSelect(style);
  };

  const handleAspectRatioSelect = (aspectRatio: AspectRatio) => {
    setSelectedAspectRatio(aspectRatio);
    onAspectRatioSelect(aspectRatio);
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 max-w-4xl">
      <div className="space-y-6">
        {/* Style Selection */}
        <div>
          <div className="flex items-center gap-2 mb-4">
            <Palette className="w-5 h-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900">Choose Visual Style</h3>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {styles.map((style) => (
              <button
                key={style.id}
                onClick={() => handleStyleSelect(style.id)}
                className={`p-4 rounded-lg border-2 transition-all text-left hover:border-blue-300 ${
                  selectedStyle === style.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 bg-white'
                }`}
              >
                <div className="text-2xl mb-2">{style.preview}</div>
                <h4 className={`font-medium mb-1 ${
                  selectedStyle === style.id ? 'text-blue-900' : 'text-gray-900'
                }`}>
                  {style.name}
                </h4>
                <p className={`text-xs ${
                  selectedStyle === style.id ? 'text-blue-700' : 'text-gray-600'
                }`}>
                  {style.description}
                </p>
              </button>
            ))}
          </div>
        </div>

        {/* Aspect Ratio Selection */}
        {selectedStyle && (
          <div>
            <div className="flex items-center gap-2 mb-4">
              <Monitor className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-semibold text-gray-900">Choose Format</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              {aspectRatios.map((format) => {
                const IconComponent = format.icon;
                return (
                  <button
                    key={format.ratio}
                    onClick={() => handleAspectRatioSelect(format.ratio)}
                    className={`p-4 rounded-lg border-2 transition-all text-left hover:border-blue-300 ${
                      selectedAspectRatio === format.ratio
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 bg-white'
                    }`}
                  >
                    <div className="flex items-center gap-3 mb-2">
                      <IconComponent 
                        size={24} 
                        className={selectedAspectRatio === format.ratio ? 'text-blue-600' : 'text-gray-600'} 
                      />
                      <div>
                        <h4 className={`font-medium ${
                          selectedAspectRatio === format.ratio ? 'text-blue-900' : 'text-gray-900'
                        }`}>
                          {format.name}
                        </h4>
                        <p className={`text-xs ${
                          selectedAspectRatio === format.ratio ? 'text-blue-600' : 'text-gray-500'
                        }`}>
                          {format.ratio} • {format.dimensions}
                        </p>
                      </div>
                    </div>
                    <p className={`text-xs ${
                      selectedAspectRatio === format.ratio ? 'text-blue-700' : 'text-gray-600'
                    }`}>
                      {format.description}
                    </p>
                  </button>
                );
              })}
            </div>
          </div>
        )}

        {/* Selection Summary */}
        {selectedStyle && selectedAspectRatio && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <h4 className="font-medium text-green-900">Selection Complete</h4>
            </div>
            <p className="text-sm text-green-700">
              Style: <span className="font-medium">{styles.find(s => s.id === selectedStyle)?.name}</span> • 
              Format: <span className="font-medium">{selectedAspectRatio}</span>
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ImageStyleSelector;

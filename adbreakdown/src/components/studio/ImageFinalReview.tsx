'use client';

import { ImageIdea, AspectRatio } from '@/types/studio';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>, 
  Monitor, 
  Edit3, 
  AlertCircle, 
  Play,
  Eye,
  Users,
  Target
} from 'lucide-react';

interface ImageFinalReviewProps {
  selectedIdea: ImageIdea;
  style: string;
  aspectRatio: AspectRatio;
  prompt: string;
  negativePrompt: string;
  onGenerate: () => void;
}

const ImageFinalReview: React.FC<ImageFinalReviewProps> = ({
  selectedIdea,
  style,
  aspectRatio,
  prompt,
  negativePrompt,
  onGenerate
}) => {
  const getAspectRatioDisplay = (ratio: AspectRatio) => {
    switch (ratio) {
      case '1:1':
        return { name: 'Square', dimensions: '1080x1080', icon: '⬜' };
      case '16:9':
        return { name: 'Landscape', dimensions: '1920x1080', icon: '📺' };
      case '9:16':
        return { name: 'Portrait', dimensions: '1080x1920', icon: '📱' };
      default:
        return { name: ratio, dimensions: 'Custom', icon: '📐' };
    }
  };

  const formatDisplay = getAspectRatioDisplay(aspectRatio);

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 max-w-4xl">
      <div className="flex items-center gap-2 mb-6">
        <Eye className="w-5 h-5 text-blue-600" />
        <h3 className="text-lg font-semibold text-gray-900">Final Review</h3>
      </div>

      <div className="space-y-6">
        {/* Concept Summary */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-3">
            <Sparkles className="w-4 h-4 text-blue-600" />
            <h4 className="font-medium text-blue-900">Selected Concept</h4>
          </div>
          <p className="text-sm text-blue-800 mb-3">{selectedIdea.summary}</p>
          
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <div className="flex items-center gap-2 mb-2">
                <Users className="w-3 h-3 text-blue-600" />
                <span className="text-xs font-medium text-blue-700">Target Audience</span>
              </div>
              <div className="flex flex-wrap gap-1">
                {selectedIdea.targetAudience.map((audience, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                  >
                    {audience}
                  </span>
                ))}
              </div>
            </div>
            
            <div>
              <div className="flex items-center gap-2 mb-2">
                <Target className="w-3 h-3 text-blue-600" />
                <span className="text-xs font-medium text-blue-700">Key Points</span>
              </div>
              <div className="flex flex-wrap gap-1">
                {selectedIdea.USPs.map((usp, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full"
                  >
                    {usp}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Style and Format */}
        <div className="grid md:grid-cols-2 gap-4">
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Palette className="w-4 h-4 text-purple-600" />
              <h4 className="font-medium text-purple-900">Visual Style</h4>
            </div>
            <p className="text-sm text-purple-800 capitalize">{style}</p>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Monitor className="w-4 h-4 text-green-600" />
              <h4 className="font-medium text-green-900">Format</h4>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-lg">{formatDisplay.icon}</span>
              <div>
                <p className="text-sm text-green-800 font-medium">{formatDisplay.name}</p>
                <p className="text-xs text-green-600">{aspectRatio} • {formatDisplay.dimensions}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Prompt Preview */}
        <div>
          <div className="flex items-center gap-2 mb-3">
            <Edit3 className="w-4 h-4 text-gray-600" />
            <h4 className="font-medium text-gray-900">Generation Prompt</h4>
          </div>
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <p className="text-sm text-gray-800 leading-relaxed">{prompt}</p>
          </div>
        </div>

        {/* Negative Prompt */}
        {negativePrompt && (
          <div>
            <div className="flex items-center gap-2 mb-3">
              <AlertCircle className="w-4 h-4 text-red-600" />
              <h4 className="font-medium text-gray-900">Negative Prompt</h4>
            </div>
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-sm text-red-800 leading-relaxed">{negativePrompt}</p>
            </div>
          </div>
        )}

        {/* Generation Info */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <AlertCircle className="w-4 h-4 text-yellow-600" />
            <h4 className="font-medium text-yellow-900">Generation Details</h4>
          </div>
          <ul className="text-sm text-yellow-800 space-y-1">
            <li>• 4 high-quality images will be generated</li>
            <li>• Generation typically takes 30-60 seconds</li>
            <li>• Images will include watermarks as per safety settings</li>
            <li>• All images will be in {formatDisplay.dimensions} resolution</li>
          </ul>
        </div>

        {/* Generate Button */}
        <div className="flex justify-center pt-4">
          <button
            onClick={onGenerate}
            className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all font-medium text-lg flex items-center gap-3 shadow-lg hover:shadow-xl transform hover:scale-105"
          >
            <Play size={20} />
            Generate My Images
          </button>
        </div>

        {/* Disclaimer */}
        <div className="text-center">
          <p className="text-xs text-gray-500">
            By generating images, you agree to use them responsibly and in accordance with our terms of service.
          </p>
        </div>
      </div>
    </div>
  );
};

export default ImageFinalReview;

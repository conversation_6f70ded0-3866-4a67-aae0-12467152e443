'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { ChatMessage, ImageGenerationState, ImageIdea, AspectRatio } from '@/types/studio';
import UnifiedInputForm, { UnifiedInputData } from './UnifiedInputForm';
import ImageIdeaCard from './ImageIdeaCard';
import ImageStyleSelector from './ImageStyleSelector';
import ImagePromptEditor from './ImagePromptEditor';
import ImageFinalReview from './ImageFinalReview';
import ImageGenerationAnimation from './ImageGenerationAnimation';
import { Bot, User } from 'lucide-react';

interface ImageChatInterfaceProps {
  state: ImageGenerationState;
  setState: React.Dispatch<React.SetStateAction<ImageGenerationState>>;
}

const ImageChatInterface: React.FC<ImageChatInterfaceProps> = ({ state, setState }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const selectedIdeaRef = useRef<ImageIdea | null>(null);
  const ideasRef = useRef<ImageIdea[]>([]);
  const styleRef = useRef<string | null>(null);
  const aspectRatioRef = useRef<AspectRatio | null>(null);
  const promptRef = useRef<string | null>(null);
  const negativePromptRef = useRef<string | null>(null);
  const animationMessageRef = useRef<string | null>(null);
  const initialMessageAddedRef = useRef<boolean>(false);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const addBotMessage = useCallback((text: string, component?: React.ReactNode) => {
    const message: ChatMessage = {
      id: `bot-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      text,
      sender: 'bot',
      component,
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, message]);
    return message.id;
  }, []);

  const updateBotMessage = useCallback((messageId: string, text: string, component?: React.ReactNode) => {
    setMessages(prev => prev.map(msg => 
      msg.id === messageId ? { ...msg, text, component } : msg
    ));
  }, []);

  const addUserMessage = useCallback((text: string) => {
    const message: ChatMessage = {
      id: `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      text,
      sender: 'user',
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, message]);
  }, []);

  // Initialize with welcome message
  useEffect(() => {
    if (!initialMessageAddedRef.current) {
      addBotMessage(
        "🎨 Welcome to the Image Ad Creator! I'll help you create stunning visual ads for your brand. Let's start by gathering some information about your business.",
        <UnifiedInputForm onSubmit={handleFormSubmit} contentType="image" />
      );
      initialMessageAddedRef.current = true;
    }
  }, [addBotMessage]);

  const handleFormSubmit = async (data: UnifiedInputData) => {
    setState(prev => ({ ...prev, url: data.url || '', step: 'generating-ideas' }));
    
    const submissionText = data.url 
      ? `Website: ${data.url}${data.brandName ? `, Brand: ${data.brandName}` : ''}${data.category ? `, Category: ${data.category}` : ''}`
      : `Brand: ${data.brandName}, Category: ${data.category}, Target: ${data.targetAudience}`;
    
    addUserMessage(submissionText);
    addBotMessage("Perfect! Let me analyze your information and generate some creative image concepts...");
    
    try {
      const response = await fetch('/api/v1/images/ideas', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to generate ideas: ${response.statusText}`);
      }
      
      const ideas = await response.json();
      ideasRef.current = ideas;
      setState(prev => ({ ...prev, ideas, step: 'idea-selection' }));
      
      addBotMessage(
        "I've generated some creative image concepts for your ad! Please review and select the one you'd like to use:",
        <div className="space-y-4">
          {ideas.map((idea: ImageIdea, index: number) => (
            <ImageIdeaCard
              key={index}
              idea={idea}
              onSelect={() => handleIdeaSelect(idea)}
              onUpdate={(updatedIdea: ImageIdea) => {
                ideasRef.current = ideasRef.current.map((existingIdea: ImageIdea, i: number) => 
                  i === index ? updatedIdea : existingIdea
                );
                if (selectedIdeaRef.current && selectedIdeaRef.current === idea) {
                  selectedIdeaRef.current = updatedIdea;
                  setState(prev => ({ ...prev, selectedIdea: updatedIdea }));
                }
              }}
            />
          ))}
        </div>
      );
    } catch (error) {
      console.error('Ideas generation error:', error);
      addBotMessage("Sorry, I encountered an error while generating ideas. Please try again.");
    }
  };

  const handleIdeaSelect = (idea: ImageIdea) => {
    selectedIdeaRef.current = idea;
    setState(prev => ({ ...prev, selectedIdea: idea, step: 'style-selection' }));
    addUserMessage(`Selected concept: ${idea.summary}`);
    
    addBotMessage(
      "Great choice! Now let's choose the visual style and aspect ratio for your image:",
      <ImageStyleSelector
        onStyleSelect={(style) => handleStyleSelect(style)}
        onAspectRatioSelect={(aspectRatio) => handleAspectRatioSelect(aspectRatio)}
      />
    );
  };

  const handleStyleSelect = (style: string) => {
    styleRef.current = style;
    setState(prev => ({ ...prev, style }));
    addUserMessage(`Selected style: ${style}`);
  };

  const handleAspectRatioSelect = (aspectRatio: AspectRatio) => {
    aspectRatioRef.current = aspectRatio;
    setState(prev => ({ ...prev, aspectRatio, step: 'prompt-editing' }));
    addUserMessage(`Selected format: ${aspectRatio}`);
    
    addBotMessage("Perfect! Now I'll generate a detailed prompt based on your selections...");
    
    const selectedIdea = selectedIdeaRef.current;
    const style = styleRef.current;
    
    if (!selectedIdea || !style) {
      addBotMessage("Error: Missing required information. Please start over.");
      return;
    }

    // Generate initial prompt
    const initialPrompt = `${selectedIdea.visualDescription}, ${style} style, professional advertising image, high quality, ${selectedIdea.USPs.join(', ')}, targeting ${selectedIdea.targetAudience.join(', ')}`;
    
    promptRef.current = initialPrompt;
    setState(prev => ({ ...prev, prompt: initialPrompt }));
    
    addBotMessage(
      "Here's your generated prompt! You can edit it to fine-tune the image generation:",
      <ImagePromptEditor
        prompt={initialPrompt}
        negativePrompt=""
        onUpdate={(prompt, negativePrompt) => {
          promptRef.current = prompt;
          negativePromptRef.current = negativePrompt;
          setState(prev => ({ ...prev, prompt, negativePrompt }));
        }}
        onNext={() => handlePromptComplete()}
      />
    );
  };

  const handlePromptComplete = () => {
    setState(prev => ({ ...prev, step: 'final-review' }));
    addUserMessage("Prompt looks good!");
    
    addBotMessage(
      "Perfect! Here's a summary of your image ad. Ready to generate?",
      <ImageFinalReview
        selectedIdea={selectedIdeaRef.current!}
        style={styleRef.current!}
        aspectRatio={aspectRatioRef.current!}
        prompt={promptRef.current!}
        negativePrompt={negativePromptRef.current || ''}
        onGenerate={() => handleImageGeneration()}
      />
    );
  };

  const handleImageGeneration = async () => {
    setState(prev => ({ ...prev, step: 'image-generation', status: 'pending' }));
    addUserMessage("Generate my images!");
    
    // Show the animation immediately
    const animationMessageId = addBotMessage(
      "🎨 Creating your stunning image ads now...",
      <ImageGenerationAnimation 
        status="processing"
        progress={0}
        message="Starting image generation"
      />
    );
    animationMessageRef.current = animationMessageId;
    
    try {
      const response = await fetch('/api/v1/images/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prompt: promptRef.current,
          negativePrompt: negativePromptRef.current || '',
          aspectRatio: aspectRatioRef.current,
          style: styleRef.current,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to generate images: ${errorData.error || response.statusText}`);
      }
      
      const result = await response.json();
      setState(prev => ({ ...prev, imageUrls: result.imageUrls, status: 'completed', step: 'completed' }));
      
      // Hide animation and show results
      if (animationMessageRef.current) {
        updateBotMessage(
          animationMessageRef.current,
          "🎉 Your image ads are ready! Here are your generated images:",
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              {result.imageUrls.map((url: string, index: number) => (
                <div key={index} className="relative group">
                  <img
                    src={url}
                    alt={`Generated image ${index + 1}`}
                    className="w-full h-auto rounded-lg shadow-md hover:shadow-lg transition-shadow"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all rounded-lg flex items-center justify-center">
                    <div className="opacity-0 group-hover:opacity-100 flex gap-2 transition-opacity">
                      <button
                        onClick={() => window.open(url, '_blank')}
                        className="bg-white text-gray-900 px-3 py-1 rounded-md text-sm font-medium hover:bg-gray-100"
                      >
                        View Full Size
                      </button>
                      <button
                        onClick={() => {
                          const link = document.createElement('a');
                          link.href = url;
                          link.download = `generated-image-${index + 1}.png`;
                          document.body.appendChild(link);
                          link.click();
                          document.body.removeChild(link);
                        }}
                        className="bg-blue-600 text-white px-3 py-1 rounded-md text-sm font-medium hover:bg-blue-700"
                      >
                        Download
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            <div className="flex justify-center">
              <button
                onClick={() => {
                  result.imageUrls.forEach((url: string, index: number) => {
                    setTimeout(() => {
                      const link = document.createElement('a');
                      link.href = url;
                      link.download = `generated-image-${index + 1}.png`;
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                    }, index * 500); // Stagger downloads
                  });
                }}
                className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all font-medium"
              >
                Download All Images
              </button>
            </div>
          </div>
        );
        animationMessageRef.current = null;
      }
      
    } catch (error) {
      console.error('Image generation error:', error);
      setState(prev => ({ ...prev, status: 'failed' }));
      
      if (animationMessageRef.current) {
        updateBotMessage(
          animationMessageRef.current,
          "Sorry, I encountered an error while generating your images. Please try again.",
          null
        );
        animationMessageRef.current = null;
      }
    }
  };

  return (
    <div className="flex flex-col h-[600px]">
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`flex items-start gap-3 max-w-[80%] ${
                message.sender === 'user' ? 'flex-row-reverse' : 'flex-row'
              }`}
            >
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  message.sender === 'user'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-600'
                }`}
              >
                {message.sender === 'user' ? <User size={16} /> : <Bot size={16} />}
              </div>
              <div
                className={`rounded-lg p-3 ${
                  message.sender === 'user'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-900'
                }`}
              >
                <p className="text-sm whitespace-pre-wrap">{message.text}</p>
                {message.component && (
                  <div className="mt-3">
                    {message.component}
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>
    </div>
  );
};

export default ImageChatInterface;

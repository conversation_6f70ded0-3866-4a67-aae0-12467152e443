'use client';

import { useState, useEffect } from 'react';
import { Edit3, Eye, AlertCircle, CheckCircle, ArrowRight } from 'lucide-react';

interface ImagePromptEditorProps {
  prompt: string;
  negativePrompt: string;
  onUpdate: (prompt: string, negativePrompt: string) => void;
  onNext: () => void;
}

const ImagePromptEditor: React.FC<ImagePromptEditorProps> = ({
  prompt,
  negativePrompt,
  onUpdate,
  onNext
}) => {
  const [editedPrompt, setEditedPrompt] = useState(prompt);
  const [editedNegativePrompt, setEditedNegativePrompt] = useState(negativePrompt);
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    setEditedPrompt(prompt);
    setEditedNegativePrompt(negativePrompt);
  }, [prompt, negativePrompt]);

  const handleSave = () => {
    onUpdate(editedPrompt, editedNegativePrompt);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedPrompt(prompt);
    setEditedNegativePrompt(negativePrompt);
    setIsEditing(false);
  };

  const promptTips = [
    "Be specific about colors, lighting, and composition",
    "Include style keywords like 'professional', 'modern', or 'minimalist'",
    "Mention the target audience or use case",
    "Add quality modifiers like 'high resolution', '4K', or 'detailed'"
  ];

  const negativePromptSuggestions = [
    "blurry, low quality, pixelated",
    "text, watermark, logo",
    "distorted, deformed, ugly",
    "dark, underexposed, overexposed"
  ];

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 max-w-4xl">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Edit3 className="w-5 h-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">Fine-tune Your Prompt</h3>
        </div>
        {!isEditing && (
          <button
            onClick={() => setIsEditing(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
          >
            Edit Prompt
          </button>
        )}
      </div>

      {isEditing ? (
        <div className="space-y-6">
          {/* Main Prompt Editor */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Main Prompt
            </label>
            <textarea
              value={editedPrompt}
              onChange={(e) => setEditedPrompt(e.target.value)}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Describe the image you want to generate..."
            />
            <p className="text-xs text-gray-500 mt-1">
              {editedPrompt.length} characters
            </p>
          </div>

          {/* Negative Prompt Editor */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Negative Prompt (Optional)
            </label>
            <textarea
              value={editedNegativePrompt}
              onChange={(e) => setEditedNegativePrompt(e.target.value)}
              rows={2}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Describe what you don't want in the image..."
            />
            <p className="text-xs text-gray-500 mt-1">
              Helps avoid unwanted elements in your image
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
            >
              Save Changes
            </button>
            <button
              onClick={handleCancel}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium"
            >
              Cancel
            </button>
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Prompt Preview */}
          <div>
            <div className="flex items-center gap-2 mb-3">
              <Eye className="w-4 h-4 text-gray-600" />
              <h4 className="font-medium text-gray-900">Current Prompt</h4>
            </div>
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <p className="text-sm text-gray-800 leading-relaxed">{prompt}</p>
            </div>
          </div>

          {/* Negative Prompt Preview */}
          {negativePrompt && (
            <div>
              <div className="flex items-center gap-2 mb-3">
                <AlertCircle className="w-4 h-4 text-gray-600" />
                <h4 className="font-medium text-gray-900">Negative Prompt</h4>
              </div>
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <p className="text-sm text-red-800 leading-relaxed">{negativePrompt}</p>
              </div>
            </div>
          )}

          {/* Continue Button */}
          <div className="flex justify-center">
            <button
              onClick={onNext}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium flex items-center gap-2"
            >
              Continue to Review
              <ArrowRight size={16} />
            </button>
          </div>
        </div>
      )}

      {/* Tips Section */}
      <div className="mt-8 grid md:grid-cols-2 gap-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-3">
            <CheckCircle className="w-4 h-4 text-blue-600" />
            <h4 className="font-medium text-blue-900">Prompt Tips</h4>
          </div>
          <ul className="space-y-2">
            {promptTips.map((tip, index) => (
              <li key={index} className="text-xs text-blue-700 flex items-start gap-2">
                <div className="w-1 h-1 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                {tip}
              </li>
            ))}
          </ul>
        </div>

        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-3">
            <AlertCircle className="w-4 h-4 text-red-600" />
            <h4 className="font-medium text-red-900">Common Negative Prompts</h4>
          </div>
          <ul className="space-y-2">
            {negativePromptSuggestions.map((suggestion, index) => (
              <li key={index} className="text-xs text-red-700 flex items-start gap-2">
                <div className="w-1 h-1 bg-red-600 rounded-full mt-2 flex-shrink-0"></div>
                {suggestion}
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default ImagePromptEditor;

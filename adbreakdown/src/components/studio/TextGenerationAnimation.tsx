'use client';

import { useState, useEffect } from 'react';
import { FileText, Sparkles, PenTool, MessageSquare, Zap } from 'lucide-react';

interface TextGenerationAnimationProps {
  status?: 'pending' | 'processing' | 'completed' | 'failed';
  progress?: number;
  message?: string;
}

const TextGenerationAnimation: React.FC<TextGenerationAnimationProps> = ({ 
  progress = 0, 
  message = 'Generating your content...' 
}) => {
  const [animationStep, setAnimationStep] = useState(0);
  const [dots, setDots] = useState('');

  // Animate the dots
  useEffect(() => {
    const dotsInterval = setInterval(() => {
      setDots(prev => {
        if (prev === '...') return '';
        return prev + '.';
      });
    }, 500);

    return () => clearInterval(dotsInterval);
  }, []);

  // Cycle through animation steps
  useEffect(() => {
    const stepInterval = setInterval(() => {
      setAnimationStep(prev => (prev + 1) % 4);
    }, 1000);

    return () => clearInterval(stepInterval);
  }, []);

  const getAnimationIcon = () => {
    switch (animationStep) {
      case 0:
        return <PenTool className="w-8 h-8 text-green-500 animate-pulse" />;
      case 1:
        return <Sparkles className="w-8 h-8 text-emerald-500 animate-pulse" />;
      case 2:
        return <MessageSquare className="w-8 h-8 text-blue-500 animate-pulse" />;
      case 3:
        return <FileText className="w-8 h-8 text-purple-500 animate-pulse" />;
      default:
        return <Zap className="w-8 h-8 text-green-500 animate-pulse" />;
    }
  };

  const getStepMessage = () => {
    if (progress < 25) return "Analyzing your brand information";
    if (progress < 50) return "Crafting compelling messaging";
    if (progress < 75) return "Optimizing for your platform";
    if (progress < 100) return "Adding final touches";
    return "Content ready!";
  };

  return (
    <div className="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-lg p-8 max-w-md mx-auto">
      <div className="text-center space-y-6">
        {/* Animated Icon */}
        <div className="flex justify-center">
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full opacity-20 animate-ping"></div>
            {getAnimationIcon()}
          </div>
        </div>

        {/* Progress Bar */}
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-700">Progress</span>
            <span className="text-sm font-medium text-green-600">{Math.round(progress)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-green-500 to-emerald-500 h-2 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>

        {/* Status Message */}
        <div className="space-y-2">
          <h3 className="text-lg font-semibold text-gray-900">
            Creating Your Content{dots}
          </h3>
          <p className="text-sm text-gray-600">
            {getStepMessage()}
          </p>
          {message && message !== 'Generating your content...' && (
            <p className="text-xs text-green-600 font-medium">
              {message}
            </p>
          )}
        </div>

        {/* Animation Steps Indicator */}
        <div className="flex justify-center space-x-2">
          {[0, 1, 2, 3].map((step) => (
            <div
              key={step}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                step === animationStep 
                  ? 'bg-green-500 scale-125' 
                  : 'bg-gray-300'
              }`}
            />
          ))}
        </div>

        {/* Fun Facts */}
        <div className="bg-white bg-opacity-50 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <Sparkles className="w-4 h-4 text-emerald-500" />
            <span className="text-xs font-medium text-gray-700">Did you know?</span>
          </div>
          <p className="text-xs text-gray-600 leading-relaxed">
            {animationStep === 0 && "AI copywriting can increase conversion rates by up to 40% when properly optimized for the target platform."}
            {animationStep === 1 && "The best ad copy combines emotional triggers with clear value propositions to drive action."}
            {animationStep === 2 && "Platform-specific optimization can improve engagement rates by 3x compared to generic content."}
            {animationStep === 3 && "Your content is being tailored to match your brand voice and target audience preferences."}
          </p>
        </div>

        {/* Estimated Time */}
        <div className="text-xs text-gray-500">
          Estimated time remaining: {Math.max(0, Math.ceil((100 - progress) / 3))} seconds
        </div>
      </div>
    </div>
  );
};

export default TextGenerationAnimation;

'use client';

import { useState, useEffect } from 'react';
import { Play, Video, Sparkles, Film, Camera } from 'lucide-react';

interface VideoGenerationAnimationProps {
  status?: 'pending' | 'processing' | 'completed' | 'failed';
  progress?: number;
  message?: string;
}

const VideoGenerationAnimation: React.FC<VideoGenerationAnimationProps> = ({ 
  progress = 0, 
  message = 'Generating your video...' 
}) => {
  const [animationStep, setAnimationStep] = useState(0);
  const [dots, setDots] = useState('');

  // Animate the dots
  useEffect(() => {
    const dotsInterval = setInterval(() => {
      setDots(prev => {
        if (prev === '...') return '';
        return prev + '.';
      });
    }, 500);

    return () => clearInterval(dotsInterval);
  }, []);

  // Animate through different steps
  useEffect(() => {
    const stepInterval = setInterval(() => {
      setAnimationStep(prev => (prev + 1) % 4);
    }, 1500);

    return () => clearInterval(stepInterval);
  }, []);

  const steps = [
    { icon: Camera, text: 'Analyzing script', color: 'text-blue-400' },
    { icon: Sparkles, text: 'Creating visuals', color: 'text-purple-400' },
    { icon: Film, text: 'Rendering scenes', color: 'text-green-400' },
    { icon: Video, text: 'Finalizing video', color: 'text-orange-400' }
  ];

  const currentStep = steps[animationStep];
  const CurrentIcon = currentStep.icon;

  return (
    <div className="bg-gray-50 rounded-lg p-8 max-w-md mx-auto">
      {/* Main Animation Container */}
      <div className="flex flex-col items-center space-y-6">
        
        {/* Pulsing Video Icon */}
        <div className="relative">
          <div className="absolute inset-0 animate-ping">
            <div className="w-16 h-16 bg-purple-200 rounded-full opacity-30"></div>
          </div>
          <div className="relative w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center">
            <Play className="w-8 h-8 text-purple-600 animate-pulse" />
          </div>
        </div>

        {/* Progress Bar */}
        <div className="w-full">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>Progress</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-1000 ease-out relative overflow-hidden"
              style={{ width: `${Math.max(progress, 10)}%` }}
            >
              {/* Shimmer effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
            </div>
          </div>
        </div>

        {/* Current Step Indicator */}
        <div className="flex items-center space-x-3 min-h-[24px]">
          <CurrentIcon className={`w-5 h-5 ${currentStep.color} animate-bounce`} />
          <span className="text-gray-700 font-medium">
            {currentStep.text}
          </span>
        </div>

        {/* Status Message */}
        <div className="text-center">
          <p className="text-gray-600 text-sm">
            {message}{dots}
          </p>
          <p className="text-xs text-gray-500 mt-1">
            This usually takes 1-3 minutes
          </p>
        </div>

        {/* Floating Particles Animation */}
        <div className="relative w-full h-16 overflow-hidden">
          {[...Array(6)].map((_, i) => (
            <div
              key={i}
              className="absolute w-2 h-2 bg-purple-300 rounded-full opacity-60 animate-float"
              style={{
                left: `${15 + i * 15}%`,
                animationDelay: `${i * 0.5}s`,
                animationDuration: '3s'
              }}
            />
          ))}
        </div>

        {/* Step Indicators */}
        <div className="flex space-x-2">
          {steps.map((_, index) => (
            <div
              key={index}
              className={`w-2 h-2 rounded-full transition-all duration-500 ${
                index === animationStep 
                  ? 'bg-purple-500 scale-150' 
                  : index < animationStep 
                    ? 'bg-green-400' 
                    : 'bg-gray-300'
              }`}
            />
          ))}
        </div>
      </div>

      <style jsx>{`
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        
        @keyframes float {
          0%, 100% { 
            transform: translateY(0px) rotate(0deg); 
            opacity: 0;
          }
          50% { 
            transform: translateY(-20px) rotate(180deg); 
            opacity: 0.6;
          }
        }
        
        .animate-shimmer {
          animation: shimmer 2s infinite;
        }
        
        .animate-float {
          animation: float 3s infinite ease-in-out;
        }
      `}</style>
    </div>
  );
};

export default VideoGenerationAnimation;
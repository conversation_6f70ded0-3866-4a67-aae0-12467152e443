'use client';

import { useState } from 'react';
import { AudioType, VoiceType, AudioMood, AudioDuration } from '@/types/studio';
import { User, Users, Clock, ArrowRight, Volume2, Heart, Zap } from 'lucide-react';

interface AudioCustomizationFormProps {
  audioType: AudioType;
  onSubmit: (voice: VoiceType, mood: AudioMood, duration: AudioDuration) => void;
}

const AudioCustomizationForm: React.FC<AudioCustomizationFormProps> = ({ audioType, onSubmit }) => {
  const [selectedVoice, setSelectedVoice] = useState<VoiceType | null>(null);
  const [selectedMood, setSelectedMood] = useState<AudioMood | null>(null);
  const [selectedDuration, setSelectedDuration] = useState<AudioDuration | null>(null);

  const voices = [
    { id: 'male-professional' as VoiceType, name: 'Male Professional', description: 'Deep, authoritative business voice', icon: User },
    { id: 'female-professional' as VoiceType, name: 'Female Professional', description: 'Clear, confident corporate voice', icon: User },
    { id: 'male-casual' as VoiceType, name: 'Male Casual', description: 'Friendly, approachable everyday voice', icon: User },
    { id: 'female-casual' as VoiceType, name: 'Female Casual', description: 'Warm, conversational tone', icon: User },
    { id: 'male-energetic' as VoiceType, name: 'Male Energetic', description: 'Upbeat, enthusiastic delivery', icon: Zap },
    { id: 'female-energetic' as VoiceType, name: 'Female Energetic', description: 'Dynamic, exciting presentation', icon: Zap },
    { id: 'narrator' as VoiceType, name: 'Narrator', description: 'Storytelling, documentary style', icon: Users },
    { id: 'child' as VoiceType, name: 'Child Voice', description: 'Young, playful voice for family brands', icon: Heart },
    { id: 'elderly' as VoiceType, name: 'Elderly Voice', description: 'Wise, experienced tone', icon: Users }
  ];

  const moods = [
    { id: 'upbeat' as AudioMood, name: 'Upbeat', description: 'Happy, positive, energetic', color: 'bg-yellow-100 text-yellow-800' },
    { id: 'calm' as AudioMood, name: 'Calm', description: 'Peaceful, relaxing, soothing', color: 'bg-blue-100 text-blue-800' },
    { id: 'energetic' as AudioMood, name: 'Energetic', description: 'High-energy, exciting, dynamic', color: 'bg-red-100 text-red-800' },
    { id: 'professional' as AudioMood, name: 'Professional', description: 'Business-like, formal, trustworthy', color: 'bg-gray-100 text-gray-800' },
    { id: 'playful' as AudioMood, name: 'Playful', description: 'Fun, lighthearted, entertaining', color: 'bg-pink-100 text-pink-800' },
    { id: 'dramatic' as AudioMood, name: 'Dramatic', description: 'Intense, powerful, impactful', color: 'bg-purple-100 text-purple-800' },
    { id: 'mysterious' as AudioMood, name: 'Mysterious', description: 'Intriguing, suspenseful, enigmatic', color: 'bg-indigo-100 text-indigo-800' },
    { id: 'romantic' as AudioMood, name: 'Romantic', description: 'Loving, intimate, emotional', color: 'bg-rose-100 text-rose-800' },
    { id: 'corporate' as AudioMood, name: 'Corporate', description: 'Business-focused, reliable, stable', color: 'bg-slate-100 text-slate-800' }
  ];

  const durations = [
    { id: '5s' as AudioDuration, name: '5 seconds', description: 'Quick sound bite or jingle' },
    { id: '10s' as AudioDuration, name: '10 seconds', description: 'Short intro or transition' },
    { id: '15s' as AudioDuration, name: '15 seconds', description: 'Brief commercial or announcement' },
    { id: '30s' as AudioDuration, name: '30 seconds', description: 'Standard radio ad length' },
    { id: '60s' as AudioDuration, name: '60 seconds', description: 'Extended commercial format' },
    { id: '2min' as AudioDuration, name: '2 minutes', description: 'Detailed presentation or story' },
    { id: '5min' as AudioDuration, name: '5 minutes', description: 'Long-form content or background music' }
  ];

  // Filter options based on audio type
  const getRelevantVoices = () => {
    switch (audioType) {
      case 'radio-ad':
        return voices.filter(v => ['male-professional', 'female-professional', 'male-energetic', 'female-energetic'].includes(v.id));
      case 'jingle':
        return voices.filter(v => ['male-energetic', 'female-energetic', 'child'].includes(v.id));
      case 'background-music':
        return []; // No voice needed for background music
      case 'voiceover':
        return voices; // All voices available
      case 'podcast-intro':
        return voices.filter(v => ['male-professional', 'female-professional', 'narrator'].includes(v.id));
      case 'sound-effect':
        return []; // No voice needed for sound effects
      default:
        return voices;
    }
  };

  const getRelevantDurations = () => {
    switch (audioType) {
      case 'radio-ad':
        return durations.filter(d => ['15s', '30s', '60s'].includes(d.id));
      case 'jingle':
        return durations.filter(d => ['5s', '10s', '15s', '30s'].includes(d.id));
      case 'background-music':
        return durations.filter(d => ['30s', '60s', '2min', '5min'].includes(d.id));
      case 'voiceover':
        return durations.filter(d => ['10s', '15s', '30s', '60s', '2min', '5min'].includes(d.id));
      case 'podcast-intro':
        return durations.filter(d => ['10s', '15s', '30s'].includes(d.id));
      case 'sound-effect':
        return durations.filter(d => ['5s', '10s'].includes(d.id));
      default:
        return durations;
    }
  };

  const handleSubmit = () => {
    if (selectedMood && selectedDuration && (selectedVoice || !getRelevantVoices().length)) {
      onSubmit(selectedVoice || 'narrator', selectedMood, selectedDuration);
    }
  };

  const relevantVoices = getRelevantVoices();
  const relevantDurations = getRelevantDurations();
  const needsVoice = relevantVoices.length > 0;

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 max-w-4xl space-y-6">
      {/* Voice Selection */}
      {needsVoice && (
        <div>
          <h4 className="text-md font-semibold text-gray-900 mb-3 flex items-center gap-2">
            <Volume2 size={18} />
            Select Voice Type
          </h4>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {relevantVoices.map((voice) => {
              const IconComponent = voice.icon;
              return (
                <button
                  key={voice.id}
                  onClick={() => setSelectedVoice(voice.id)}
                  className={`p-3 rounded-lg border-2 transition-all text-left ${
                    selectedVoice === voice.id
                      ? 'border-orange-500 bg-orange-50'
                      : 'border-gray-200 bg-white hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center gap-2 mb-1">
                    <IconComponent size={16} className={selectedVoice === voice.id ? 'text-orange-600' : 'text-gray-600'} />
                    <span className={`text-sm font-medium ${selectedVoice === voice.id ? 'text-orange-900' : 'text-gray-900'}`}>
                      {voice.name}
                    </span>
                  </div>
                  <p className={`text-xs ${selectedVoice === voice.id ? 'text-orange-700' : 'text-gray-600'}`}>
                    {voice.description}
                  </p>
                </button>
              );
            })}
          </div>
        </div>
      )}

      {/* Mood Selection */}
      <div>
        <h4 className="text-md font-semibold text-gray-900 mb-3 flex items-center gap-2">
          <Heart size={18} />
          Choose Mood & Style
        </h4>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {moods.map((mood) => (
            <button
              key={mood.id}
              onClick={() => setSelectedMood(mood.id)}
              className={`p-3 rounded-lg border-2 transition-all text-left ${
                selectedMood === mood.id
                  ? 'border-orange-500 bg-orange-50'
                  : 'border-gray-200 bg-white hover:border-gray-300'
              }`}
            >
              <div className="flex items-center gap-2 mb-1">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${mood.color}`}>
                  {mood.name}
                </span>
              </div>
              <p className={`text-xs ${selectedMood === mood.id ? 'text-orange-700' : 'text-gray-600'}`}>
                {mood.description}
              </p>
            </button>
          ))}
        </div>
      </div>

      {/* Duration Selection */}
      <div>
        <h4 className="text-md font-semibold text-gray-900 mb-3 flex items-center gap-2">
          <Clock size={18} />
          Audio Duration
        </h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {relevantDurations.map((duration) => (
            <button
              key={duration.id}
              onClick={() => setSelectedDuration(duration.id)}
              className={`p-3 rounded-lg border-2 transition-all text-left ${
                selectedDuration === duration.id
                  ? 'border-orange-500 bg-orange-50'
                  : 'border-gray-200 bg-white hover:border-gray-300'
              }`}
            >
              <span className={`text-sm font-medium block mb-1 ${selectedDuration === duration.id ? 'text-orange-900' : 'text-gray-900'}`}>
                {duration.name}
              </span>
              <p className={`text-xs ${selectedDuration === duration.id ? 'text-orange-700' : 'text-gray-600'}`}>
                {duration.description}
              </p>
            </button>
          ))}
        </div>
      </div>

      {/* Submit Button */}
      {selectedMood && selectedDuration && (needsVoice ? selectedVoice : true) && (
        <div className="flex justify-center pt-4">
          <button
            onClick={handleSubmit}
            className="px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors font-medium flex items-center gap-2"
          >
            Continue to Review
            <ArrowRight size={16} />
          </button>
        </div>
      )}

      {/* Selection Summary */}
      {(selectedVoice || selectedMood || selectedDuration) && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h5 className="text-sm font-medium text-gray-900 mb-2">Current Selection:</h5>
          <div className="flex flex-wrap gap-2">
            {selectedVoice && (
              <span className="px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">
                Voice: {relevantVoices.find(v => v.id === selectedVoice)?.name}
              </span>
            )}
            {selectedMood && (
              <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                Mood: {moods.find(m => m.id === selectedMood)?.name}
              </span>
            )}
            {selectedDuration && (
              <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
                Duration: {relevantDurations.find(d => d.id === selectedDuration)?.name}
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default AudioCustomizationForm;

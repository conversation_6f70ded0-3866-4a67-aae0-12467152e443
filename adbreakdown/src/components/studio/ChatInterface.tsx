'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { ChatMessage, VideoGenerationState, VideoIdea, Voice, AspectRatio } from '@/types/studio';
import UnifiedInputForm, { UnifiedInputData } from './UnifiedInputForm';
import IdeaCard from './IdeaCard';
import ParameterSelectors from './ParameterSelectors';
import ScriptEditor from './ScriptEditor';
import FinalReview from './FinalReview';
import VideoGenerationAnimation from './VideoGenerationAnimation';
import { Bot, User } from 'lucide-react';

interface ChatInterfaceProps {
  state: VideoGenerationState;
  setState: React.Dispatch<React.SetStateAction<VideoGenerationState>>;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({ state, setState }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const selectedIdeaRef = useRef<VideoIdea | null>(null);
  const ideasRef = useRef<VideoIdea[]>([]);
  const durationRef = useRef<number | null>(null);
  const voiceRef = useRef<Voice | null>(null);
  const aspectRatioRef = useRef<AspectRatio | null>(null);
  const scriptRef = useRef<any | null>(null);
  const animationMessageRef = useRef<string | null>(null);
  const initialMessageAddedRef = useRef<boolean>(false);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const addBotMessage = useCallback((text: string, component?: React.ReactNode) => {
    const message: ChatMessage = {
      id: `bot-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      text,
      sender: 'bot',
      component,
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, message]);
    return message.id;
  }, []);

  const updateBotMessage = useCallback((messageId: string, text: string, component?: React.ReactNode) => {
    setMessages(prev => prev.map(msg => 
      msg.id === messageId ? { ...msg, text, component } : msg
    ));
  }, []);

  const addUserMessage = useCallback((text: string) => {
    const message: ChatMessage = {
      id: `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      text,
      sender: 'user',
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, message]);
  }, []);

  const handleVoiceSelect = (voice: any) => {
    console.log('handleVoiceSelect called with:', voice);
    voiceRef.current = voice;
    setState(prev => ({ ...prev, voice, step: 'aspect-ratio-selection' }));
    addUserMessage(`Selected voice: ${voice.name}`);
    
    addBotMessage(
      "Excellent! What aspect ratio would you like for your video?",
      <ParameterSelectors
        type="aspectRatio"
        onSelect={(aspectRatio) => handleAspectRatioSelect(aspectRatio as any)}
      />
    );
  };

  const handleDurationSelect = useCallback((duration: number) => {
    console.log('handleDurationSelect called with:', duration);
    durationRef.current = duration;
    setState(prev => ({ ...prev, duration, step: 'voice-selection' }));
    addUserMessage(`${duration} seconds`);
    
    addBotMessage(
      "Great! Now let's choose a voice for your ad:",
      <ParameterSelectors
        type="voice"
        onSelect={(voice) => handleVoiceSelect(voice as any)}
      />
    );
  }, [setState, addUserMessage, addBotMessage]);

  const handleIdeaSelect = useCallback((idea: any) => {
    console.log('handleIdeaSelect called with:', idea);
    selectedIdeaRef.current = idea;
    setState(prev => ({ ...prev, selectedIdea: idea, step: 'duration-selection' }));
    addUserMessage(`Selected: ${idea.summary}`);
    
    addBotMessage(
      "Perfect choice! How long would you like your video to be?",
      <ParameterSelectors
        type="duration"
        onSelect={(duration) => handleDurationSelect(duration as number)}
      />
    );
  }, [setState, addUserMessage, addBotMessage, handleDurationSelect]);


  const handleUnifiedSubmit = useCallback(async (data: UnifiedInputData) => {
    // Determine what information we have
    const hasUrl = data.url?.trim();
    const hasManualData = data.brandName?.trim() || data.category || data.targetAudience?.trim();
    
    // Create user message based on what was provided
    let userMessage = '';
    if (hasUrl && hasManualData) {
      userMessage = `URL: ${data.url} + Manual details: ${data.brandName} - ${data.category} targeting ${data.targetAudience}`;
    } else if (hasUrl) {
      userMessage = data.url!;
    } else {
      userMessage = `Manual info: ${data.brandName} - ${data.category} targeting ${data.targetAudience}`;
    }
    
    addUserMessage(userMessage);
    setState(prev => ({ ...prev, url: data.url || '', step: 'generating-ideas' }));
    
    // Create appropriate bot message
    if (hasUrl && hasManualData) {
      addBotMessage("Perfect! I'll analyze your website and use your specific brand details to generate creative concepts...");
    } else if (hasUrl) {
      addBotMessage("Great! Let me analyze your product and generate some creative concepts...");
    } else {
      addBotMessage("Perfect! Let me generate some creative concepts based on your brand information...");
    }
    
    try {
      let requestData: any = {};
      
      if (hasUrl && !hasManualData) {
        // URL only
        requestData = {
          url: data.url,
          videoDescription: data.videoDescription
        };
      } else if (!hasUrl && hasManualData) {
        // Manual data only
        const mockUrl = `https://manual-brand-info.local?brand=${encodeURIComponent(data.brandName || '')}&category=${encodeURIComponent(data.category || '')}&audience=${encodeURIComponent(data.targetAudience || '')}`;
        requestData = {
          url: mockUrl,
          manualData: {
            brandName: data.brandName,
            category: data.category,
            subCategory: data.subCategory,
            targetAudience: data.targetAudience,
            market: data.market,
            usp: data.usp,
            videoDescription: data.videoDescription
          }
        };
      } else {
        // Both URL and manual data - hybrid approach
        requestData = {
          url: data.url,
          videoDescription: data.videoDescription,
          manualData: {
            brandName: data.brandName,
            category: data.category,
            subCategory: data.subCategory,
            targetAudience: data.targetAudience,
            market: data.market,
            usp: data.usp
            // Note: videoDescription is passed at top level, not in manualData
          }
        };
      }
      
      const response = await fetch('/api/v1/ideas/generate-from-url', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData),
      });
      
      if (!response.ok) throw new Error('Failed to generate ideas');
      
      const ideas = await response.json();
      ideasRef.current = ideas;
      setState(prev => ({ ...prev, ideas, step: 'idea-selection' }));
      
      addBotMessage(
        "I've generated some creative concepts for your ad! Please review and select the one you'd like to use:",
        <div className="space-y-4">
          {ideas.map((idea: any, index: number) => (
            <IdeaCard
              key={index}
              idea={idea}
              onSelect={() => handleIdeaSelect(idea)}
              onUpdate={(updatedIdea: VideoIdea) => {
                // Update the idea in the ideas array
                ideasRef.current = ideasRef.current.map((existingIdea: VideoIdea, i: number) => 
                  i === index ? updatedIdea : existingIdea
                );
                // If this idea was already selected, update the ref as well
                if (selectedIdeaRef.current && selectedIdeaRef.current === idea) {
                  selectedIdeaRef.current = updatedIdea;
                  setState(prev => ({ ...prev, selectedIdea: updatedIdea }));
                }
                console.log('Updated idea:', updatedIdea);
              }}
            />
          ))}
        </div>
      );
    } catch (error) {
      addBotMessage("Sorry, I encountered an error while generating ideas. Please try again.");
      setState(prev => ({ ...prev, step: 'url-input' }));
    }
  }, [addUserMessage, setState, addBotMessage, handleIdeaSelect]);

  useEffect(() => {
    if (messages.length === 0 && state.step === 'init' && !initialMessageAddedRef.current) {
      initialMessageAddedRef.current = true;
      addBotMessage(
        "Hello! I'm here to help you create an amazing video ad. You can provide a website URL or enter your brand information manually.",
        <UnifiedInputForm key="unified-input-form" onSubmit={handleUnifiedSubmit} />
      );
    }
  }, [handleUnifiedSubmit, messages.length, addBotMessage, state.step]);

  const handleAspectRatioSelect = async (aspectRatio: any) => {
    console.log('handleAspectRatioSelect called with:', aspectRatio);
    aspectRatioRef.current = aspectRatio;
    console.log('Ref values:', {
      selectedIdea: selectedIdeaRef.current,
      duration: durationRef.current,
      voice: voiceRef.current,
      aspectRatio: aspectRatioRef.current
    });
    
    setState(prev => ({ ...prev, aspectRatio, step: 'script-generation' }));
    addUserMessage(`Format: ${aspectRatio}`);
    
    addBotMessage("Perfect! Now I'll generate a script based on your selections...");
    
    const selectedIdea = selectedIdeaRef.current;
    const duration = durationRef.current;
    
    if (!selectedIdea || !duration) {
      addBotMessage("Error: Missing required information. Please start over.");
      console.error('Missing data from refs:', { selectedIdea, duration });
      return;
    }
    
    try {
      console.log('Sending script generation request:', {
        idea: selectedIdea,
        duration: duration
      });
      
      const response = await fetch('/api/v1/scripts/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          idea: selectedIdea,
          duration: duration,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        console.error('Script generation error:', errorData);
        throw new Error('Failed to generate script');
      }
      
      const script = await response.json();
      scriptRef.current = script;
      setState(prev => ({ ...prev, script, step: 'script-editing' }));
      
      addBotMessage(
        "Here's your generated script! You can edit any part of it:",
        <ScriptEditor
          script={script}
          onUpdate={(updatedScript) => {
            scriptRef.current = updatedScript;
            setState(prev => ({ ...prev, script: updatedScript }));
          }}
          onNext={() => handleScriptComplete()}
        />
      );
    } catch (error) {
      console.error('Script generation error:', error);
      addBotMessage("Sorry, I encountered an error while generating the script. Please try again.");
    }
  };

  const handleScriptComplete = () => {
    setState(prev => ({ ...prev, step: 'final-review' }));
    addUserMessage("Script looks good!");
    
    addBotMessage(
      "Perfect! Here's a summary of your video ad. Ready to generate?",
      <FinalReview
        selectedIdea={selectedIdeaRef.current!}
        duration={durationRef.current!}
        voice={voiceRef.current!}
        aspectRatio={aspectRatioRef.current!}
        script={scriptRef.current!}
        onGenerate={() => handleVideoGeneration()}
      />
    );
  };

  const pollVideoStatus = async (jobId: string) => {
    let attempts = 0;
    let notFoundCount = 0;
    const maxAttempts = 150; // 12.5 minutes total (150 * 5 seconds) - generous for video generation
    const maxNotFoundAttempts = 5; // Allow 5 consecutive "not found" errors before giving up
    
    const interval = setInterval(async () => {
      attempts++;
      console.log(`Polling attempt ${attempts} for job ${jobId}`);
      
      try {
        const response = await fetch(`/api/v1/videos/status/${jobId}`);
        
        if (!response.ok) {
          if (response.status === 404) {
            notFoundCount++;
            console.warn(`Job ${jobId} not found (attempt ${notFoundCount}/${maxNotFoundAttempts})`);
            
            if (notFoundCount >= maxNotFoundAttempts) {
              console.error(`Job ${jobId} not found after ${notFoundCount} attempts`);
              clearInterval(interval);
              addBotMessage("Video generation job not found after multiple attempts. Please try again.");
              return;
            }
            // Continue polling, don't exit on first 404
            return;
          }
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        // Reset not found counter on successful response
        notFoundCount = 0;
        
        const data = await response.json();
        const { status, videoUrl, message, progress } = data;
        
        console.log(`Job ${jobId} status:`, { 
          status, 
          progress, 
          message, 
          hasVideoUrl: !!videoUrl,
          videoUrlPreview: videoUrl ? videoUrl.substring(0, 100) + '...' : null,
          fullResponse: data
        });
        
        setState(prev => ({ ...prev, status, progress }));
        
        // Update the animation with current progress
        if (animationMessageRef.current && (progress || 0) < 100) {
          updateBotMessage(
            animationMessageRef.current,
            "🎬 Creating your amazing video ad...",
            <VideoGenerationAnimation 
              status={status as any}
              progress={progress || 0}
              message={message || 'Processing your video...'}
            />
          );
        } else if (animationMessageRef.current && (progress || 0) >= 100) {
          // Hide animation when progress reaches 100%
          updateBotMessage(
            animationMessageRef.current,
            "🎉 Video generation completed! Preparing your video...",
            null
          );
          animationMessageRef.current = null;
        }
        
        if (status === 'completed') {
          clearInterval(interval);
          animationMessageRef.current = null; // Clear the animation message reference
          if (videoUrl) {
            console.log('Setting completed state with videoUrl:', videoUrl);
            setState(prev => ({ ...prev, videoUrl, step: 'completed' }));
            addBotMessage(
              "🎉 Your video ad is ready! Here it is:",
              <div className="space-y-4">
                <div className="text-sm text-gray-600 mb-2">
                  Video URL: {videoUrl.substring(0, 50)}...
                </div>
                <video
                  src={videoUrl}
                  controls
                  className="w-full max-w-md rounded-lg"
                  onError={(e) => {
                    console.error('Video load error:', e);
                    console.error('Failed to load video from:', videoUrl);
                  }}
                  onLoadStart={() => console.log('Video load started')}
                  onCanPlay={() => console.log('Video can play')}
                />
                <div className="flex gap-2">
                  <button
                    onClick={() => {
                      console.log('Opening video URL:', videoUrl);
                      window.open(videoUrl, '_blank');
                    }}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    Download Video
                  </button>
                  <button
                    onClick={() => window.location.reload()}
                    className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                  >
                    Create Another
                  </button>
                </div>
              </div>
            );
          } else {
            addBotMessage("Video generation completed but no video URL was provided. Please try again.");
          }
        } else if (status === 'failed') {
          clearInterval(interval);
          animationMessageRef.current = null; // Clear the animation message reference
          addBotMessage(`Video generation failed: ${message || 'Unknown error'}`);
        } else if (attempts >= maxAttempts) {
          clearInterval(interval);
          animationMessageRef.current = null; // Clear the animation message reference
          addBotMessage("Video generation is taking too long. Please try again.");
        }
      } catch (error) {
        console.error('Polling error:', error);
        if (attempts >= maxAttempts) {
          clearInterval(interval);
          animationMessageRef.current = null; // Clear the animation message reference
          addBotMessage("Error checking video status. Please try again.");
        }
      }
    }, 5000); // Poll every 5 seconds instead of 3
  };

  const handleVideoGeneration = async () => {
    setState(prev => ({ ...prev, step: 'video-generation', status: 'pending' }));
    addUserMessage("Generate my video!");
    
    // Show the animation immediately
    const animationMessageId = addBotMessage(
      "🎬 Lights, camera, action! I'm creating your video ad now...",
      <VideoGenerationAnimation 
        status="processing"
        progress={0}
        message="Starting video generation"
      />
    );
    animationMessageRef.current = animationMessageId;
    
    console.log('Full state before video generation:', state);
    console.log('Ref values:', {
      selectedIdea: selectedIdeaRef.current,
      duration: durationRef.current,
      voice: voiceRef.current
    });
    
    const currentScript = scriptRef.current || state.script;
    
    if (!currentScript || !currentScript.scenes || currentScript.scenes.length === 0) {
      addBotMessage("Error: Script is missing. Please try regenerating the script.");
      console.error('Script missing:', { scriptRef: scriptRef.current, stateScript: state.script });
      return;
    }
    
    try {
      const currentAspectRatio = aspectRatioRef.current || state.aspectRatio;
      
      console.log('Video generation data:', {
        script: currentScript,
        voiceId: voiceRef.current?.id,
        aspectRatio: currentAspectRatio,
      });

      const response = await fetch('/api/v1/videos/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          script: currentScript,
          voiceId: voiceRef.current?.id,
          aspectRatio: currentAspectRatio,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        console.error('Video generation error:', errorData);
        throw new Error(`Failed to start video generation: ${errorData.error || response.statusText}`);
      }
      
      const { jobId } = await response.json();
      setState(prev => ({ ...prev, jobId, status: 'processing' }));
      
      pollVideoStatus(jobId);
    } catch (error) {
      addBotMessage("Sorry, I encountered an error while starting video generation. Please try again.");
      setState(prev => ({ ...prev, status: 'failed' }));
    }
  };


  return (
    <div className="flex flex-col h-[600px]">
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`flex items-start gap-3 max-w-[80%] ${
                message.sender === 'user' ? 'flex-row-reverse' : 'flex-row'
              }`}
            >
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  message.sender === 'user'
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-200 text-gray-600'
                }`}
              >
                {message.sender === 'user' ? <User size={16} /> : <Bot size={16} />}
              </div>
              <div
                className={`rounded-lg p-3 ${
                  message.sender === 'user'
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-100 text-gray-900'
                }`}
              >
                <p className="text-sm">{message.text}</p>
                {message.component && (
                  <div className="mt-3">{message.component}</div>
                )}
              </div>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>
      
    </div>
  );
};

export default ChatInterface;
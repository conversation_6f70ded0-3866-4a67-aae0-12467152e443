'use client';

import { useState } from 'react';
import { Building2, Users, Target, Globe, Lightbulb, MessageSquare } from 'lucide-react';

interface ManualInfoFormProps {
  onSubmit: (data: ManualInfoData) => void;
  onCancel: () => void;
}

export interface ManualInfoData {
  brandName: string;
  category: string;
  subCategory: string;
  targetAudience: string;
  market: string;
  usp: string;
  otherInstructions: string;
}

const ManualInfoForm: React.FC<ManualInfoFormProps> = ({ onSubmit, onCancel }) => {
  const [formData, setFormData] = useState<ManualInfoData>({
    brandName: '',
    category: '',
    subCategory: '',
    targetAudience: '',
    market: '',
    usp: '',
    otherInstructions: ''
  });

  const [isLoading, setIsLoading] = useState(false);

  const categories = [
    'Fashion & Apparel',
    'Technology & Electronics',
    'Health & Fitness',
    'Food & Beverage',
    'Home & Garden',
    'Beauty & Personal Care',
    'Automotive',
    'Finance & Banking',
    'Education',
    'Entertainment',
    'Travel & Tourism',
    'Sports & Recreation',
    'Other'
  ];

  const markets = [
    'United States',
    'United Kingdom',
    'Canada',
    'Australia',
    'Germany',
    'France',
    'Spain',
    'Italy',
    'Netherlands',
    'India',
    'Japan',
    'Singapore',
    'Other'
  ];

  const handleInputChange = (field: keyof ManualInfoData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate required fields
    if (!formData.brandName.trim() || !formData.category || !formData.targetAudience.trim()) {
      alert('Please fill in all required fields (Brand Name, Category, and Target Audience)');
      return;
    }

    setIsLoading(true);
    try {
      await onSubmit(formData);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 max-w-2xl">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Manual Brand Information</h3>
        <p className="text-sm text-gray-600">Please provide the following information to help us create your video ad</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Brand Name */}
        <div>
          <label className="flex items-center gap-2 text-sm font-medium text-gray-900 mb-2">
            <Building2 size={16} />
            Brand Name *
          </label>
          <input
            type="text"
            value={formData.brandName}
            onChange={(e) => handleInputChange('brandName', e.target.value)}
            placeholder="Enter your brand name"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            disabled={isLoading}
            required
          />
        </div>

        {/* Category */}
        <div>
          <label className="flex items-center gap-2 text-sm font-medium text-gray-900 mb-2">
            <Target size={16} />
            Category *
          </label>
          <select
            value={formData.category}
            onChange={(e) => handleInputChange('category', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            disabled={isLoading}
            required
          >
            <option value="">Select a category</option>
            {categories.map((category) => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>
        </div>

        {/* Sub Category */}
        <div>
          <label className="flex items-center gap-2 text-sm font-medium text-gray-900 mb-2">
            <Target size={16} />
            Sub Category
          </label>
          <input
            type="text"
            value={formData.subCategory}
            onChange={(e) => handleInputChange('subCategory', e.target.value)}
            placeholder="e.g. Running shoes, Smartphones, Skincare"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            disabled={isLoading}
          />
        </div>

        {/* Target Audience */}
        <div>
          <label className="flex items-center gap-2 text-sm font-medium text-gray-900 mb-2">
            <Users size={16} />
            Target Audience *
          </label>
          <input
            type="text"
            value={formData.targetAudience}
            onChange={(e) => handleInputChange('targetAudience', e.target.value)}
            placeholder="e.g. Young professionals aged 25-35, Fitness enthusiasts"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            disabled={isLoading}
            required
          />
        </div>

        {/* Market */}
        <div>
          <label className="flex items-center gap-2 text-sm font-medium text-gray-900 mb-2">
            <Globe size={16} />
            Market
          </label>
          <select
            value={formData.market}
            onChange={(e) => handleInputChange('market', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            disabled={isLoading}
          >
            <option value="">Select a market</option>
            {markets.map((market) => (
              <option key={market} value={market}>
                {market}
              </option>
            ))}
          </select>
        </div>

        {/* USP */}
        <div>
          <label className="flex items-center gap-2 text-sm font-medium text-gray-900 mb-2">
            <Lightbulb size={16} />
            Unique Selling Proposition (USP)
          </label>
          <input
            type="text"
            value={formData.usp}
            onChange={(e) => handleInputChange('usp', e.target.value)}
            placeholder="What makes your product/service unique?"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            disabled={isLoading}
          />
        </div>

        {/* Other Instructions */}
        <div>
          <label className="flex items-center gap-2 text-sm font-medium text-gray-900 mb-2">
            <MessageSquare size={16} />
            Other Instructions
          </label>
          <textarea
            value={formData.otherInstructions}
            onChange={(e) => handleInputChange('otherInstructions', e.target.value)}
            placeholder="Any additional information or specific requirements for your ad..."
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
            disabled={isLoading}
          />
        </div>

        {/* Buttons */}
        <div className="flex gap-3 pt-2">
          <button
            type="submit"
            disabled={isLoading || !formData.brandName.trim() || !formData.category || !formData.targetAudience.trim()}
            className="flex-1 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          >
            {isLoading ? 'Processing...' : 'Generate Video Ideas'}
          </button>
          
          <button
            type="button"
            onClick={onCancel}
            disabled={isLoading}
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
};

export default ManualInfoForm;
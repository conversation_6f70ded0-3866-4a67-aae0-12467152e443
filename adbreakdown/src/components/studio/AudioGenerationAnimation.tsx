'use client';

import { useState, useEffect } from 'react';
import { Music, Radio, Volume2, Mic, Zap } from 'lucide-react';

interface AudioGenerationAnimationProps {
  status?: 'pending' | 'processing' | 'completed' | 'failed';
  progress?: number;
  message?: string;
}

const AudioGenerationAnimation: React.FC<AudioGenerationAnimationProps> = ({ 
  progress = 0, 
  message = 'Generating your audio...' 
}) => {
  const [animationStep, setAnimationStep] = useState(0);
  const [dots, setDots] = useState('');

  // Animate the dots
  useEffect(() => {
    const dotsInterval = setInterval(() => {
      setDots(prev => {
        if (prev === '...') return '';
        return prev + '.';
      });
    }, 500);

    return () => clearInterval(dotsInterval);
  }, []);

  // Cycle through animation steps
  useEffect(() => {
    const stepInterval = setInterval(() => {
      setAnimationStep(prev => (prev + 1) % 4);
    }, 1000);

    return () => clearInterval(stepInterval);
  }, []);

  const getAnimationIcon = () => {
    switch (animationStep) {
      case 0:
        return <Mic className="w-8 h-8 text-orange-500 animate-pulse" />;
      case 1:
        return <Music className="w-8 h-8 text-amber-500 animate-pulse" />;
      case 2:
        return <Volume2 className="w-8 h-8 text-red-500 animate-pulse" />;
      case 3:
        return <Radio className="w-8 h-8 text-purple-500 animate-pulse" />;
      default:
        return <Zap className="w-8 h-8 text-orange-500 animate-pulse" />;
    }
  };

  const getStepMessage = () => {
    if (progress < 25) return "Analyzing your brand voice";
    if (progress < 50) return "Composing audio elements";
    if (progress < 75) return "Mixing and mastering";
    if (progress < 100) return "Finalizing your audio";
    return "Audio ready!";
  };

  const getAudioWaves = () => {
    return Array.from({ length: 8 }, (_, i) => (
      <div
        key={i}
        className={`w-1 bg-gradient-to-t from-orange-400 to-amber-400 rounded-full animate-pulse`}
        style={{
          height: `${Math.random() * 40 + 20}px`,
          animationDelay: `${i * 0.1}s`,
          animationDuration: `${0.8 + Math.random() * 0.4}s`
        }}
      />
    ));
  };

  return (
    <div className="bg-gradient-to-br from-orange-50 to-amber-50 border border-orange-200 rounded-lg p-8 max-w-md mx-auto">
      <div className="text-center space-y-6">
        {/* Animated Icon */}
        <div className="flex justify-center">
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-orange-400 to-amber-400 rounded-full opacity-20 animate-ping"></div>
            {getAnimationIcon()}
          </div>
        </div>

        {/* Audio Waves Visualization */}
        <div className="flex justify-center items-end gap-1 h-16">
          {getAudioWaves()}
        </div>

        {/* Progress Bar */}
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-700">Progress</span>
            <span className="text-sm font-medium text-orange-600">{Math.round(progress)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-orange-500 to-amber-500 h-2 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>

        {/* Status Message */}
        <div className="space-y-2">
          <h3 className="text-lg font-semibold text-gray-900">
            Creating Your Audio{dots}
          </h3>
          <p className="text-sm text-gray-600">
            {getStepMessage()}
          </p>
          {message && message !== 'Generating your audio...' && (
            <p className="text-xs text-orange-600 font-medium">
              {message}
            </p>
          )}
        </div>

        {/* Animation Steps Indicator */}
        <div className="flex justify-center space-x-2">
          {[0, 1, 2, 3].map((step) => (
            <div
              key={step}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                step === animationStep 
                  ? 'bg-orange-500 scale-125' 
                  : 'bg-gray-300'
              }`}
            />
          ))}
        </div>

        {/* Fun Facts */}
        <div className="bg-white bg-opacity-50 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <Music className="w-4 h-4 text-amber-500" />
            <span className="text-xs font-medium text-gray-700">Did you know?</span>
          </div>
          <p className="text-xs text-gray-600 leading-relaxed">
            {animationStep === 0 && "AI-generated audio can match human voice quality while being produced 100x faster than traditional methods."}
            {animationStep === 1 && "The perfect jingle can increase brand recall by up to 96% compared to ads without music."}
            {animationStep === 2 && "Professional audio mixing involves balancing frequencies, dynamics, and spatial positioning for optimal listening."}
            {animationStep === 3 && "Your audio is being optimized for various playback devices from smartphones to professional sound systems."}
          </p>
        </div>

        {/* Estimated Time */}
        <div className="text-xs text-gray-500">
          Estimated time remaining: {Math.max(0, Math.ceil((100 - progress) / 1.5))} seconds
        </div>
      </div>
    </div>
  );
};

export default AudioGenerationAnimation;

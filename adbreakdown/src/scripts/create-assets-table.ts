/**
 * Create Generated Assets Table
 * This script creates the generated_assets table in Supabase
 */

import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = 'https://elossghirdivbobfycob.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsb3NzZ2hpcmRpdmJvYmZ5Y29iIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDkxOTM1MCwiZXhwIjoyMDY2NDk1MzUwfQ.xbxYhEBpCLSdihJ98jFrsLDHR7wHR5ZtGXqaGWXJFn8';

async function createGeneratedAssetsTable() {
  console.log('🔧 Creating generated_assets table...');

  const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

  const createTableSQL = `
    -- Create generated_assets table
    CREATE TABLE IF NOT EXISTS public.generated_assets (
      id uuid NOT NULL DEFAULT uuid_generate_v4(),
      user_id uuid NOT NULL,
      
      -- Asset Information
      asset_type text NOT NULL CHECK (asset_type IN ('image', 'video', 'audio', 'text')),
      asset_name text NOT NULL,
      asset_url text NOT NULL,
      file_size integer,
      mime_type text,
      
      -- Generation Context
      generation_type text NOT NULL CHECK (generation_type IN ('imagen', 'veo', 'text_generation', 'audio_generation')),
      prompt text,
      negative_prompt text,
      generation_parameters jsonb,
      
      -- Metadata
      thumbnail_url text,
      duration_seconds integer,
      dimensions jsonb,
      
      -- Organization
      tags text[],
      is_favorite boolean DEFAULT false,
      folder_path text,
      
      -- Status and Timestamps
      status text DEFAULT 'completed' CHECK (status IN ('generating', 'completed', 'failed', 'deleted')),
      created_at timestamp with time zone DEFAULT now(),
      updated_at timestamp with time zone DEFAULT now(),
      
      CONSTRAINT generated_assets_pkey PRIMARY KEY (id),
      CONSTRAINT generated_assets_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE
    );
  `;

  try {
    // Execute the SQL using a raw query
    const { error } = await supabase.rpc('exec', { sql: createTableSQL });
    
    if (error) {
      console.error('❌ Error creating table:', error);
      return false;
    }

    console.log('✅ Table created successfully');

    // Create indexes
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_generated_assets_user_id ON public.generated_assets(user_id);',
      'CREATE INDEX IF NOT EXISTS idx_generated_assets_type ON public.generated_assets(asset_type);',
      'CREATE INDEX IF NOT EXISTS idx_generated_assets_generation_type ON public.generated_assets(generation_type);',
      'CREATE INDEX IF NOT EXISTS idx_generated_assets_created_at ON public.generated_assets(created_at DESC);',
      'CREATE INDEX IF NOT EXISTS idx_generated_assets_status ON public.generated_assets(status);',
      'CREATE INDEX IF NOT EXISTS idx_generated_assets_is_favorite ON public.generated_assets(is_favorite);'
    ];

    for (const indexSQL of indexes) {
      const { error: indexError } = await supabase.rpc('exec', { sql: indexSQL });
      if (indexError) {
        console.warn('⚠️ Warning creating index:', indexError.message);
      }
    }

    console.log('✅ Indexes created');

    // Enable RLS
    const rlsSQL = `
      ALTER TABLE public.generated_assets ENABLE ROW LEVEL SECURITY;
      
      CREATE POLICY IF NOT EXISTS "Users can only access their own generated assets" 
      ON public.generated_assets FOR ALL USING (true);
    `;

    const { error: rlsError } = await supabase.rpc('exec', { sql: rlsSQL });
    if (rlsError) {
      console.warn('⚠️ Warning setting up RLS:', rlsError.message);
    } else {
      console.log('✅ RLS policies created');
    }

    return true;

  } catch (error) {
    console.error('❌ Failed to create table:', error);
    return false;
  }
}

// Run the script
createGeneratedAssetsTable()
  .then((success) => {
    if (success) {
      console.log('🎉 Generated assets table setup completed!');
    } else {
      console.log('💥 Setup failed');
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });

'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Send, Building2, Image, Video, Volume2, FileText, MessageSquare, Link, Wand2, Users, Mic, Music, PenTool, FileEdit, BookOpen } from 'lucide-react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface Brand {
  id: string;
  brand_name: string;
  website_url?: string;
  description?: string;
  industry?: string;
  created_at: string;
}

type CategoryType = 'image' | 'video' | 'audio' | 'text' | null

export default function CreatePage() {
  const [message, setMessage] = useState('')
  const [selectedBrand, setSelectedBrand] = useState<string>('default')
  const [brands, setBrands] = useState<Brand[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<CategoryType>(null)
  const router = useRouter()

  // Fetch user's brands
  useEffect(() => {
    fetchBrands()
  }, [])

  const fetchBrands = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/brands')
      if (response.ok) {
        const data = await response.json()
        setBrands(data.brands || [])
      }
    } catch (error) {
      console.error('Error fetching brands:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleChatSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (message.trim()) {
      router.push('/studio/create1')
    }
  }

  const handleUrlToVideo = () => {
    router.push('/studio/create2')
  }

  const handleUrlToImage = () => {
    router.push('/studio/create4')
  }

  const categories = [
    { type: 'image' as CategoryType, name: 'Image', icon: Image, color: 'bg-blue-500' },
    { type: 'video' as CategoryType, name: 'Video', icon: Video, color: 'bg-purple-500' },
    { type: 'audio' as CategoryType, name: 'Audio', icon: Volume2, color: 'bg-green-500' },
    { type: 'text' as CategoryType, name: 'Text', icon: FileText, color: 'bg-orange-500' }
  ]

  const subCategories = {
    image: [
      { name: 'Text to Image Ads', enabled: true, icon: MessageSquare, onClick: handleUrlToImage },
      { name: 'URL to Image Ads', enabled: true, icon: Link, onClick: handleUrlToImage }
    ],
    video: [
      { name: 'URL to Video', enabled: true, onClick: handleUrlToVideo, icon: Link },
      { name: 'Text to Video', enabled: true, onClick: handleUrlToVideo, icon: Link },
      { name: 'Image to Video', enabled: false, icon: Image, onClick: undefined },
      { name: 'Avatar Video', enabled: false, icon: Users, onClick: undefined }
    ],
    audio: [
      { name: 'Text to Speech', enabled: false, icon: Mic, onClick: undefined },
      { name: 'Sound Effects Generator', enabled: false, icon: Volume2, onClick: undefined },
      { name: 'Jingle Maker', enabled: false, icon: Music, onClick: undefined }
    ],
    text: [
      { name: 'Google Ads Copy', enabled: false, icon: PenTool, onClick: undefined },
      { name: 'Script Generation', enabled: false, icon: FileEdit, onClick: undefined },
      { name: 'Blogs and Articles', enabled: false, icon: BookOpen, onClick: undefined }
    ]
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      {/* Brand Header */}
      <div className="mb-8">
        <div className="flex items-center gap-3">
          <span className="text-sm text-gray-600">Brand:</span>
          <Select value={selectedBrand} onValueChange={setSelectedBrand}>
            <SelectTrigger className="w-[200px] bg-white">
              <div className="flex items-center gap-2">
                <SelectValue placeholder="Select brand" />
              </div>
            </SelectTrigger>
            <SelectContent>
              {brands.map((brand) => (
                <SelectItem key={brand.id} value={brand.id}>
                  <div className="flex items-center gap-2">
                    <Building2 className="w-4 h-4 text-gray-500" />
                    {brand.brand_name}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {loading && (
            <span className="text-xs text-gray-400">Loading brands...</span>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto">
        {/* Greeting */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-semibold text-gray-900 mb-12">
            What do you want to create?
          </h1>
        </div>

        {/* Category Selection */}
        <div className="flex justify-center gap-4 mt-8 mb-8">
          {categories.map((category) => {
            const IconComponent = category.icon
            return (
              <button
                key={category.type}
                onClick={() => setSelectedCategory(selectedCategory === category.type ? null : category.type)}
                className={`
                  flex items-center gap-3 px-2 py-2 rounded-2xl font-medium transition-all
                  ${selectedCategory === category.type
                    ? `${category.color} text-white shadow-lg` 
                    : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'
                  }
                `}
              >
                <IconComponent size={20} />
                {category.name}
              </button>
            )
          })}
        </div>

        {/* Sub-Category Icons */}
        {selectedCategory && (
          <div className="flex justify-center gap-3 mb-8">
            {subCategories[selectedCategory].map((subCategory, index) => {
              const IconComponent = subCategory.icon
              return (
                <button
                  key={index}
                  onClick={subCategory.enabled ? subCategory.onClick : undefined}
                  disabled={!subCategory.enabled}
                  className={`
                    flex flex-col items-center gap-2 p-3 rounded-xl text-xs font-medium transition-all min-w-[80px]
                    ${subCategory.enabled 
                      ? 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200 cursor-pointer shadow-sm' 
                      : 'bg-gray-100 text-gray-400 cursor-not-allowed border border-gray-200'
                    }
                  `}
                >
                  <IconComponent size={24} />
                  <span className="text-center leading-tight">{subCategory.name}</span>
                </button>
              )
            })}
          </div>
        )}

        {/* Chat Interface */}
        <div className="mb-12">
          <form onSubmit={handleChatSubmit} className="relative">
          <div className="flex items-center bg-white rounded-3xl border-2 border-transparent bg-gradient-to-r from-blue-100 to-purple-100 p-2 min-h-[72px]">
            <input
              type="text"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Type a message..."
              className="flex-1 px-6 py-6 bg-white rounded-3xl border-0 focus:outline-none text-gray-700 placeholder-gray-400 text-lg"
            />
            <button
              type="submit"
              className="p-4 m-2 bg-purple-500 hover:bg-purple-600 rounded-full text-white transition-colors"
            >
              <Send size={24} />
            </button>
          </div>
          </form>
        </div>

      </div>

      {/* Floating Action Button 
      <div className="fixed bottom-8 right-8">
        <button className="w-14 h-14 bg-purple-500 hover:bg-purple-600 rounded-full flex items-center justify-center text-white shadow-lg transition-colors">
          <span className="text-2xl">💬</span>
        </button>
      </div>
      */}
      
    </div>
  )
}
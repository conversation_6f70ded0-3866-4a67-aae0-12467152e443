'use client';

import { useState } from 'react';
import ImageChatInterface from '@/components/studio/ImageChatInterface';
import { ImageGenerationState } from '@/types/studio';
import { CheckCircle, Circle, Clock, Zap, Image, Settings, Sparkles, Palette } from 'lucide-react';

const Create4Page = () => {
  const [state, setState] = useState<ImageGenerationState>({
    step: 'init',
    url: '',
    ideas: [],
    selectedIdea: null,
    style: null,
    aspectRatio: null,
    prompt: null,
    negativePrompt: null,
    jobId: null,
    imageUrls: null,
    status: 'idle'
  });

  const steps = [
    { 
      id: 'init',
      title: 'Provide Information', 
      description: 'Add your website URL or brand details', 
      icon: Circle,
      completed: false,
      active: state.step === 'init'
    },
    { 
      id: 'idea-selection',
      title: 'Select Concept', 
      description: 'Choose from AI-generated image concepts', 
      icon: Sparkles,
      completed: ['style-selection', 'aspect-ratio-selection', 'prompt-editing', 'final-review', 'image-generation', 'completed'].includes(state.step),
      active: state.step === 'idea-selection'
    },
    { 
      id: 'style-selection',
      title: 'Choose Style', 
      description: 'Select visual style and aspect ratio', 
      icon: Palette,
      completed: ['prompt-editing', 'final-review', 'image-generation', 'completed'].includes(state.step),
      active: ['style-selection', 'aspect-ratio-selection'].includes(state.step)
    },
    { 
      id: 'prompt-editing',
      title: 'Edit Prompt', 
      description: 'Fine-tune your image generation prompt', 
      icon: Settings,
      completed: ['final-review', 'image-generation', 'completed'].includes(state.step),
      active: state.step === 'prompt-editing'
    },
    { 
      id: 'generate',
      title: 'Generate Images', 
      description: 'Create your stunning image ads', 
      icon: Image,
      completed: state.step === 'completed',
      active: ['final-review', 'image-generation'].includes(state.step)
    }
  ];

  return (
    <div className="h-screen bg-gray-50 flex">
      {/* Main Content - Left 2/3 */}
      <div className="flex-1 flex flex-col">
        <ImageChatInterface state={state} setState={setState} />
      </div>

      {/* Steps Sidebar - Right 1/3 */}
      <div className="w-1/3 bg-white border-l border-gray-200 p-4 flex flex-col max-h-screen overflow-hidden">
        <h2 className="text-base font-semibold text-gray-900 mb-4">Image Creation Steps</h2>
        
        <div className="space-y-2 overflow-y-auto">
          {steps.map((step, index) => {
            const IconComponent = step.completed ? CheckCircle : step.icon;
            return (
              <div key={step.id} className={`flex items-start gap-2 p-3 rounded-lg transition-all ${
                step.active ? 'bg-blue-50 border border-blue-200' : 
                step.completed ? 'bg-green-50 border border-green-200' : 'bg-gray-50'
              }`}>
                <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                  step.completed ? 'bg-green-600 text-white' :
                  step.active ? 'bg-blue-600 text-white' : 'bg-gray-300 text-gray-600'
                }`}>
                  <IconComponent size={12} />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className={`text-sm font-medium ${
                    step.active ? 'text-blue-900' : 
                    step.completed ? 'text-green-900' : 'text-gray-700'
                  }`}>
                    {step.title}
                  </h3>
                  <p className={`text-xs ${
                    step.active ? 'text-blue-700' : 
                    step.completed ? 'text-green-700' : 'text-gray-500'
                  }`}>
                    {step.description}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
        
        {/* Progress Indicator */}
        <div className="mt-2 p-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <span className="text-xs font-medium text-gray-700">Progress</span>
            <span className="text-xs font-medium text-blue-600">
              {steps.filter(s => s.completed).length}/{steps.length}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-1.5">
            <div 
              className="bg-gradient-to-r from-blue-600 to-cyan-600 h-1.5 rounded-full transition-all duration-300"
              style={{ width: `${(steps.filter(s => s.completed).length / steps.length) * 100}%` }}
            />
          </div>
        </div>

        {/* Tips Section */}
        <div className="mt-3 p-3 bg-cyan-50 rounded-lg">
          <div className="flex items-center gap-2 mb-1">
            <Zap className="w-3 h-3 text-cyan-600" />
            <h3 className="text-xs font-medium text-cyan-900">Pro Tip</h3>
          </div>
          <p className="text-xs text-cyan-700 leading-relaxed">
            {state.step === 'init' && "Provide detailed brand information for better image concepts!"}
            {state.step === 'idea-selection' && "Choose the concept that best represents your brand vision."}
            {['style-selection', 'aspect-ratio-selection'].includes(state.step) && "Consider your target platform when choosing style and format."}
            {state.step === 'prompt-editing' && "Fine-tune the prompt to match your exact vision."}
            {['final-review', 'image-generation'].includes(state.step) && "Your images will be ready in just a few moments!"}
            {state.step === 'completed' && "Amazing! Your image ads are ready to captivate your audience."}
          </p>
        </div>
      </div>
    </div>
  );
};

export default Create4Page;

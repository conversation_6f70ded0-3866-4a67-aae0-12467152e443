'use client';

import { useState } from 'react';
import AudioChatInterface from '@/components/studio/AudioChatInterface';
import { AudioGenerationState } from '@/types/studio';
import { CheckCircle, Circle, Clock, Zap, Music, Settings, Sparkles, Radio } from 'lucide-react';

const Create5Page = () => {
  const [state, setState] = useState<AudioGenerationState>({
    step: 'init',
    url: '',
    ideas: [],
    selectedIdea: null,
    audioType: null,
    voice: null,
    mood: null,
    duration: null,
    generatedAudio: null,
    jobId: null,
    status: 'idle'
  });

  const steps = [
    { 
      id: 'init',
      title: 'Provide Information', 
      description: 'Add your website URL or brand details', 
      icon: Circle,
      completed: false,
      active: state.step === 'init'
    },
    { 
      id: 'idea-selection',
      title: 'Select Audio Type', 
      description: 'Choose what type of audio to generate', 
      icon: Sparkles,
      completed: ['audio-customization', 'voice-selection', 'final-review', 'audio-generation', 'completed'].includes(state.step),
      active: state.step === 'idea-selection'
    },
    { 
      id: 'audio-customization',
      title: 'Customize Audio', 
      description: 'Set voice, mood, and duration preferences', 
      icon: Radio,
      completed: ['final-review', 'audio-generation', 'completed'].includes(state.step),
      active: ['audio-customization', 'voice-selection'].includes(state.step)
    },
    { 
      id: 'final-review',
      title: 'Review & Edit', 
      description: 'Review and fine-tune your audio settings', 
      icon: Settings,
      completed: ['audio-generation', 'completed'].includes(state.step),
      active: state.step === 'final-review'
    },
    { 
      id: 'generate',
      title: 'Generate Audio', 
      description: 'Create your professional audio content', 
      icon: Music,
      completed: state.step === 'completed',
      active: ['audio-generation'].includes(state.step)
    }
  ];

  return (
    <div className="h-screen bg-gray-50 flex">
      {/* Main Content - Left 2/3 */}
      <div className="flex-1 flex flex-col">
        <AudioChatInterface state={state} setState={setState} />
      </div>

      {/* Steps Sidebar - Right 1/3 */}
      <div className="w-1/3 bg-white border-l border-gray-200 p-4 flex flex-col max-h-screen overflow-hidden">
        <h2 className="text-base font-semibold text-gray-900 mb-4">Audio Creation Steps</h2>
        
        <div className="space-y-2 overflow-y-auto">
          {steps.map((step, index) => {
            const IconComponent = step.completed ? CheckCircle : step.icon;
            return (
              <div key={step.id} className={`flex items-start gap-2 p-3 rounded-lg transition-all ${
                step.active ? 'bg-orange-50 border border-orange-200' : 
                step.completed ? 'bg-orange-50 border border-orange-200' : 'bg-gray-50'
              }`}>
                <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                  step.completed ? 'bg-orange-600 text-white' :
                  step.active ? 'bg-orange-600 text-white' : 'bg-gray-300 text-gray-600'
                }`}>
                  <IconComponent size={12} />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className={`text-sm font-medium ${
                    step.active ? 'text-orange-900' : 
                    step.completed ? 'text-orange-900' : 'text-gray-700'
                  }`}>
                    {step.title}
                  </h3>
                  <p className={`text-xs ${
                    step.active ? 'text-orange-700' : 
                    step.completed ? 'text-orange-700' : 'text-gray-500'
                  }`}>
                    {step.description}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
        
        {/* Progress Indicator */}
        <div className="mt-2 p-3 bg-gradient-to-r from-orange-50 to-amber-50 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <span className="text-xs font-medium text-gray-700">Progress</span>
            <span className="text-xs font-medium text-orange-600">
              {steps.filter(s => s.completed).length}/{steps.length}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-1.5">
            <div 
              className="bg-gradient-to-r from-orange-600 to-amber-600 h-1.5 rounded-full transition-all duration-300"
              style={{ width: `${(steps.filter(s => s.completed).length / steps.length) * 100}%` }}
            />
          </div>
        </div>

        {/* Tips Section */}
        <div className="mt-3 p-3 bg-amber-50 rounded-lg">
          <div className="flex items-center gap-2 mb-1">
            <Zap className="w-3 h-3 text-amber-600" />
            <h3 className="text-xs font-medium text-amber-900">Pro Tip</h3>
          </div>
          <p className="text-xs text-amber-700 leading-relaxed">
            {state.step === 'init' && "Provide detailed brand information for better audio content generation!"}
            {state.step === 'idea-selection' && "Choose the audio type that best fits your campaign needs."}
            {['audio-customization', 'voice-selection'].includes(state.step) && "Consider your target audience when selecting voice and mood."}
            {state.step === 'final-review' && "Review all settings before generating your audio content."}
            {state.step === 'audio-generation' && "Your professional audio will be ready shortly!"}
            {state.step === 'completed' && "Perfect! Your audio content is ready to engage your audience."}
          </p>
        </div>
      </div>
    </div>
  );
};

export default Create5Page;

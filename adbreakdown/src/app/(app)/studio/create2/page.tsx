'use client';

import { useState } from 'react';
import ChatInterface from '@/components/studio/ChatInterface';
import { VideoGenerationState } from '@/types/studio';
import { CheckCircle, Circle, Clock, Zap, Video, Settings, Sparkles } from 'lucide-react';

const Create2Page = () => {
  const [state, setState] = useState<VideoGenerationState>({
    step: 'init',
    url: '',
    ideas: [],
    selectedIdea: null,
    duration: null,
    voice: null,
    aspectRatio: null,
    script: null,
    jobId: null,
    videoUrl: null,
    status: 'idle'
  });

  const steps = [
    { 
      id: 'init',
      title: 'Provide Information', 
      description: 'Add your website URL or brand details', 
      icon: Circle,
      completed: false,
      active: state.step === 'init'
    },
    { 
      id: 'idea-selection',
      title: 'Select Concept', 
      description: 'Choose from AI-generated video concepts', 
      icon: Sparkles,
      completed: ['duration-selection', 'voice-selection', 'aspect-ratio-selection', 'script-generation', 'script-editing', 'final-review', 'video-generation', 'completed'].includes(state.step),
      active: state.step === 'idea-selection'
    },
    { 
      id: 'parameters',
      title: 'Set Parameters', 
      description: 'Choose duration, voice, and aspect ratio', 
      icon: Settings,
      completed: ['script-generation', 'script-editing', 'final-review', 'video-generation', 'completed'].includes(state.step),
      active: ['duration-selection', 'voice-selection', 'aspect-ratio-selection'].includes(state.step)
    },
    { 
      id: 'script',
      title: 'Review Script', 
      description: 'Edit and finalize your video script', 
      icon: Clock,
      completed: ['final-review', 'video-generation', 'completed'].includes(state.step),
      active: ['script-generation', 'script-editing'].includes(state.step)
    },
    { 
      id: 'generate',
      title: 'Generate Video', 
      description: 'Create your amazing video ad', 
      icon: Video,
      completed: state.step === 'completed',
      active: ['final-review', 'video-generation'].includes(state.step)
    }
  ];

  return (
    <div className="h-screen bg-gray-50 flex">
      {/* Main Content - Left 2/3 */}
      <div className="flex-1 flex flex-col">
        <ChatInterface state={state} setState={setState} />
      </div>

      {/* Steps Sidebar - Right 1/3 */}
      <div className="w-1/3 bg-white border-l border-gray-200 p-4 flex flex-col max-h-screen overflow-hidden">
        <h2 className="text-base font-semibold text-gray-900 mb-4">Video Creation Steps</h2>
        
        <div className="space-y-2 overflow-y-auto">
          {steps.map((step, index) => {
            const IconComponent = step.completed ? CheckCircle : step.icon;
            return (
              <div key={step.id} className={`flex items-start gap-2 p-3 rounded-lg transition-all ${
                step.active ? 'bg-purple-50 border border-purple-200' : 
                step.completed ? 'bg-green-50 border border-green-200' : 'bg-gray-50'
              }`}>
                <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                  step.completed ? 'bg-green-600 text-white' :
                  step.active ? 'bg-purple-600 text-white' : 'bg-gray-300 text-gray-600'
                }`}>
                  <IconComponent size={12} />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className={`text-sm font-medium ${
                    step.active ? 'text-purple-900' : 
                    step.completed ? 'text-green-900' : 'text-gray-700'
                  }`}>
                    {step.title}
                  </h3>
                  <p className={`text-xs ${
                    step.active ? 'text-purple-700' : 
                    step.completed ? 'text-green-700' : 'text-gray-500'
                  }`}>
                    {step.description}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
        
        {/* Progress Indicator */}
        <div className="mt-2 p-3 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <span className="text-xs font-medium text-gray-700">Progress</span>
            <span className="text-xs font-medium text-purple-600">
              {steps.filter(s => s.completed).length}/{steps.length}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-1.5">
            <div 
              className="bg-gradient-to-r from-purple-600 to-blue-600 h-1.5 rounded-full transition-all duration-300"
              style={{ width: `${(steps.filter(s => s.completed).length / steps.length) * 100}%` }}
            />
          </div>
        </div>

        {/* Tips Section */}
        <div className="mt-3 p-3 bg-blue-50 rounded-lg">
          <div className="flex items-center gap-2 mb-1">
            <Zap className="w-3 h-3 text-blue-600" />
            <h3 className="text-xs font-medium text-blue-900">Pro Tip</h3>
          </div>
          <p className="text-xs text-blue-700 leading-relaxed">
            {state.step === 'init' && "Provide both a URL and manual details for the best results!"}
            {state.step === 'idea-selection' && "Choose the concept that best matches your brand voice."}
            {['duration-selection', 'voice-selection', 'aspect-ratio-selection'].includes(state.step) && "Consider your target platform when choosing parameters."}
            {['script-generation', 'script-editing'].includes(state.step) && "Review and edit the script to match your brand tone."}
            {['final-review', 'video-generation'].includes(state.step) && "Your video will be ready in just a few minutes!"}
            {state.step === 'completed' && "Great job! Your video is ready to share with the world."}
          </p>
        </div>
      </div>
    </div>
  );
};

export default Create2Page;
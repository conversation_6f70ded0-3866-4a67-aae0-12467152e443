'use client'

import { useState, useEffect, useCallback, Suspense } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/hooks/useAuth'
import Link from 'next/link'
import { useRouter, useSearchParams } from 'next/navigation'
import { BarChart3, Sparkles, Calendar, Compass, Building2, ArrowRight, FolderOpen } from 'lucide-react'
import PublicAnalysisCard from '@/components/studio/PublicAnalysisCard'

interface DashboardStats {
  totalAnalyses: number
  completedAnalyses: number
  avgSentiment: number
  creditsRemaining: number
}

// Component that uses useSearchParams - needs to be wrapped in Suspense
function SearchParamsHandler() {
  const router = useRouter()
  const searchParams = useSearchParams()

  useEffect(() => {
    const analyzeOnLoad = searchParams.get('analyze')
    const youtubeUrlForAnalysis = localStorage.getItem('youtubeUrlForAnalysis')

    if (analyzeOnLoad === 'true' && youtubeUrlForAnalysis) {
      // Handle auto-analyze on load if needed
      localStorage.removeItem('youtubeUrlForAnalysis')
      router.replace('/studio', undefined) // Clean up URL
    }
  }, [searchParams, router])

  return null
}

export default function StudioOverview() {
  const { isAuthenticated, loading: authLoading } = useAuth()
  const router = useRouter()
  const [stats, setStats] = useState<DashboardStats>({
    totalAnalyses: 0,
    completedAnalyses: 0,
    avgSentiment: 0,
    creditsRemaining: 10 // This should come from user profile
  })

  useEffect(() => {
    // You can fetch user stats here if needed
    const fetchStats = async () => {
      if (!isAuthenticated) return
      
      try {
        // Fetch user stats/credits from API
        // For now, we'll use default values
        setStats({
          totalAnalyses: 0,
          completedAnalyses: 0,
          avgSentiment: 0,
          creditsRemaining: 10
        })
      } catch (error) {
        console.error('Error fetching user stats:', error)
      }
    }

    if (!authLoading) {
      fetchStats()
    }
  }, [isAuthenticated, authLoading])

  const createPublicAnalysis = useCallback(async (youtubeUrl: string) => {
    try {
      console.log('Creating public analysis for URL:', youtubeUrl)
      const response = await fetch('/api/analyses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ youtubeUrl }),
      })

      if (!response.ok) {
        throw new Error('Failed to create analysis')
      }

      const data = await response.json()
      console.log('Analysis created:', data)
      
      // Redirect to the analysis page
      if (data.analysis_id) {
        router.push(`/ad/${data.analysis_id}`)
      } else {
        console.error('No analysis ID returned')
        // Stay on studio page if no ID returned
      }
    } catch (error) {
      console.error('Error creating analysis:', error)
      // Stay on studio page on error
    }
  }, [router])

  // Handle pending analysis URL after login redirect - create public analysis
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      try {
        const pendingUrl = localStorage.getItem('pendingAnalysisUrl')
        if (pendingUrl) {
          localStorage.removeItem('pendingAnalysisUrl')
          createPublicAnalysis(pendingUrl)
        }
      } catch (error) {
        console.warn('localStorage not available or error accessing it:', error)
        // Continue without pending analysis - graceful degradation
      }
    }
  }, [isAuthenticated, authLoading, router, createPublicAnalysis])

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-center">Please sign in to access your studio</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <Link href="/sign-in">
              <Button className="bg-blue-600 hover:bg-blue-700">
                Sign In
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }

  const studioFeatures = [
    {
      title: "Optimise",
      description: "Optimise your ad performance with AI insights",
      icon: <BarChart3 className="h-6 w-6" />,
      href: "/studio/optimise",
      color: "from-blue-500 to-blue-600"
    },
    {
      title: "Create", 
      description: "AI-powered ad creation and optimization tools",
      icon: <Sparkles className="h-6 w-6" />,
      href: "/studio/create",
      color: "from-purple-500 to-purple-600",
      beta: true
    },
    {
      title: "Plan",
      description: "Schedule and plan your ad campaigns", 
      icon: <Calendar className="h-6 w-6" />,
      href: "/studio/plan",
      color: "from-violet-500 to-violet-600",
      comingSoon: true
    },
    {
      title: "Explore",
      description: "Discover trends and market insights",
      icon: <Compass className="h-6 w-6" />,
      href: "/studio/explore", 
      color: "from-teal-500 to-teal-600",
      comingSoon: true
    },
    {
      title: "Brand",
      description: "Manage your brand identity and guidelines",
      icon: <Building2 className="h-6 w-6" />,
      href: "/studio/brand",
      color: "from-rose-500 to-rose-600"
    },
    {
      title: "Assets",
      description: "View and manage your AI-generated content",
      icon: <FolderOpen className="h-6 w-6" />,
      href: "/studio/assets",
      color: "from-emerald-500 to-emerald-600"
    }
  ]

  return (
    <div className="flex flex-1 flex-col gap-6">
      <Suspense fallback={<div>Loading...</div>}>
        <SearchParamsHandler />
      </Suspense>

      {/* Quick Start Analysis - Moved to top with reduced height */}
      <div className="mb-6">
        <div className="h-72 overflow-hidden"> {/* Fixed height container with overflow hidden - increased slightly */}
          <PublicAnalysisCard creditsRemaining={stats.creditsRemaining} />
        </div>
      </div>

      {/* Studio Features Grid */}
      <div className="mb-8">
       {/*  <h2 className="text-xl font-semibold text-gray-900 mb-4">Features</h2> */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {studioFeatures.map((feature, index) => (
            <Card key={index} className={`group transition-all duration-200 ${
              feature.comingSoon 
                ? 'opacity-50 cursor-not-allowed' 
                : 'hover:shadow-lg cursor-pointer'
            }`}>
              {feature.comingSoon ? (
                <div className="relative">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className={`p-3 rounded-lg bg-gradient-to-r ${feature.color} text-white`}>
                        {feature.icon}
                      </div>
                      <Badge className="text-xs px-2 py-1 bg-orange-50 text-orange-700 border-orange-200">
                        Coming Soon
                      </Badge>
                    </div>
                    <CardTitle className="text-lg">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 mb-3">{feature.description}</p>
                    <div className="flex items-center text-gray-400 font-medium">
                      <span className="text-sm">Get Started</span>
                      <ArrowRight className="h-4 w-4 ml-1" />
                    </div>
                  </CardContent>
                </div>
              ) : (
                <Link href={feature.href}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className={`p-3 rounded-lg bg-gradient-to-r ${feature.color} text-white`}>
                        {feature.icon}
                      </div>
                      {feature.beta && (
                        <Badge className="text-xs px-2 py-1 bg-blue-50 text-blue-700 border-blue-200">
                          Beta
                        </Badge>
                      )}
                    </div>
                    <CardTitle className="text-lg">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 mb-3">{feature.description}</p>
                    <div className="flex items-center text-blue-600 group-hover:text-blue-700 font-medium">
                      <span className="text-sm">Get Started</span>
                      <ArrowRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                    </div>
                  </CardContent>
                </Link>
              )}
            </Card>
          ))}
        </div>
      </div>

    </div>
  )
}
'use client';

import { useState } from 'react';
import TextChatInterface from '@/components/studio/TextChatInterface';
import { TextGenerationState } from '@/types/studio';
import { CheckCircle, Circle, Clock, Zap, FileText, Settings, Sparkles, PenTool } from 'lucide-react';

const Create3Page = () => {
  const [state, setState] = useState<TextGenerationState>({
    step: 'init',
    url: '',
    ideas: [],
    selectedIdea: null,
    contentType: null,
    platform: null,
    tone: null,
    length: null,
    generatedContent: null,
    jobId: null,
    status: 'idle'
  });

  const steps = [
    { 
      id: 'init',
      title: 'Provide Information', 
      description: 'Add your website URL or brand details', 
      icon: Circle,
      completed: false,
      active: state.step === 'init'
    },
    { 
      id: 'idea-selection',
      title: 'Select Content Type', 
      description: 'Choose what type of content to generate', 
      icon: Sparkles,
      completed: ['content-customization', 'tone-selection', 'final-review', 'text-generation', 'completed'].includes(state.step),
      active: state.step === 'idea-selection'
    },
    { 
      id: 'content-customization',
      title: 'Customize Content', 
      description: 'Set platform, tone, and length preferences', 
      icon: PenTool,
      completed: ['final-review', 'text-generation', 'completed'].includes(state.step),
      active: ['content-customization', 'tone-selection'].includes(state.step)
    },
    { 
      id: 'final-review',
      title: 'Review & Edit', 
      description: 'Review and fine-tune your content', 
      icon: Settings,
      completed: ['text-generation', 'completed'].includes(state.step),
      active: state.step === 'final-review'
    },
    { 
      id: 'generate',
      title: 'Generate Content', 
      description: 'Create your compelling ad copy', 
      icon: FileText,
      completed: state.step === 'completed',
      active: ['text-generation'].includes(state.step)
    }
  ];

  return (
    <div className="h-screen bg-gray-50 flex">
      {/* Main Content - Left 2/3 */}
      <div className="flex-1 flex flex-col">
        <TextChatInterface state={state} setState={setState} />
      </div>

      {/* Steps Sidebar - Right 1/3 */}
      <div className="w-1/3 bg-white border-l border-gray-200 p-4 flex flex-col max-h-screen overflow-hidden">
        <h2 className="text-base font-semibold text-gray-900 mb-4">Text Creation Steps</h2>
        
        <div className="space-y-2 overflow-y-auto">
          {steps.map((step, index) => {
            const IconComponent = step.completed ? CheckCircle : step.icon;
            return (
              <div key={step.id} className={`flex items-start gap-2 p-3 rounded-lg transition-all ${
                step.active ? 'bg-green-50 border border-green-200' : 
                step.completed ? 'bg-green-50 border border-green-200' : 'bg-gray-50'
              }`}>
                <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                  step.completed ? 'bg-green-600 text-white' :
                  step.active ? 'bg-green-600 text-white' : 'bg-gray-300 text-gray-600'
                }`}>
                  <IconComponent size={12} />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className={`text-sm font-medium ${
                    step.active ? 'text-green-900' : 
                    step.completed ? 'text-green-900' : 'text-gray-700'
                  }`}>
                    {step.title}
                  </h3>
                  <p className={`text-xs ${
                    step.active ? 'text-green-700' : 
                    step.completed ? 'text-green-700' : 'text-gray-500'
                  }`}>
                    {step.description}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
        
        {/* Progress Indicator */}
        <div className="mt-2 p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <span className="text-xs font-medium text-gray-700">Progress</span>
            <span className="text-xs font-medium text-green-600">
              {steps.filter(s => s.completed).length}/{steps.length}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-1.5">
            <div 
              className="bg-gradient-to-r from-green-600 to-emerald-600 h-1.5 rounded-full transition-all duration-300"
              style={{ width: `${(steps.filter(s => s.completed).length / steps.length) * 100}%` }}
            />
          </div>
        </div>

        {/* Tips Section */}
        <div className="mt-3 p-3 bg-emerald-50 rounded-lg">
          <div className="flex items-center gap-2 mb-1">
            <Zap className="w-3 h-3 text-emerald-600" />
            <h3 className="text-xs font-medium text-emerald-900">Pro Tip</h3>
          </div>
          <p className="text-xs text-emerald-700 leading-relaxed">
            {state.step === 'init' && "Provide detailed brand information for better content generation!"}
            {state.step === 'idea-selection' && "Choose the content type that best fits your campaign goals."}
            {['content-customization', 'tone-selection'].includes(state.step) && "Consider your target audience when selecting tone and platform."}
            {state.step === 'final-review' && "Review and edit the generated content to match your brand voice."}
            {state.step === 'text-generation' && "Your content will be ready in just a few moments!"}
            {state.step === 'completed' && "Great! Your ad copy is ready to drive conversions."}
          </p>
        </div>
      </div>
    </div>
  );
};

export default Create3Page;

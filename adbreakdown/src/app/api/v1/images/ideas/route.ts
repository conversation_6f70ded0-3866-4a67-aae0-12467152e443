import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

interface ManualInfoData {
  brandName: string;
  category: string;
  subCategory: string;
  targetAudience: string;
  market: string;
  usp: string;
  videoDescription: string;
}

export async function POST(request: NextRequest) {
  try {
    const { url, brandName, category, subCategory, targetAudience, market, usp, videoDescription } = await request.json();

    console.log('Generating image ideas for:', { url, brandName, category });

    let websiteContent: string;
    
    // Check if we have manual data or URL
    if (brandName && category && targetAudience && usp) {
      // Manual data provided
      const manualData: ManualInfoData = {
        brandName,
        category,
        subCategory: subCategory || '',
        targetAudience,
        market: market || '',
        usp,
        videoDescription: videoDescription || ''
      };
      websiteContent = formatManualDataAsWebContent(manualData);
    } else if (url) {
      // URL provided
      websiteContent = await scrapeWebsite(url);
    } else {
      return NextResponse.json(
        { error: 'Either URL or complete brand information (brandName, category, targetAudience, usp) is required' },
        { status: 400 }
      );
    }
    
    // Generate image ideas using Gemini AI
    const ideas = await generateImageIdeasWithAI(websiteContent, url);

    return NextResponse.json(ideas);
  } catch (error) {
    console.error('Error generating image ideas:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      { error: 'Failed to generate image ideas: ' + errorMessage },
      { status: 500 }
    );
  }
}

function formatManualDataAsWebContent(data: ManualInfoData): string {
  return `
Title: ${data.brandName} - ${data.category}${data.subCategory ? ` | ${data.subCategory}` : ''}
Description: ${data.brandName} offers ${data.category.toLowerCase()}${data.subCategory ? ` specializing in ${data.subCategory.toLowerCase()}` : ''} targeting ${data.targetAudience}${data.market ? ` in ${data.market}` : ''}.
${data.usp ? `Unique Selling Proposition: ${data.usp}` : ''}
${data.videoDescription ? `Additional Context: ${data.videoDescription}` : ''}

Brand Information:
- Brand Name: ${data.brandName}
- Category: ${data.category}
${data.subCategory ? `- Sub Category: ${data.subCategory}` : ''}
- Target Audience: ${data.targetAudience}
${data.market ? `- Market: ${data.market}` : ''}
${data.usp ? `- Key Value Proposition: ${data.usp}` : ''}
${data.videoDescription ? `- Additional Context: ${data.videoDescription}` : ''}
`.trim();
}

async function scrapeWebsite(url: string): Promise<string> {
  try {
    console.log('Scraping website:', url);
    
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch website: ${response.status}`);
    }

    const html = await response.text();
    
    // Extract text content from HTML
    const textContent = html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
      .replace(/<[^>]+>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();

    // Extract key sections
    const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
    const descriptionMatch = html.match(/<meta[^>]*name=["']description["'][^>]*content=["']([^"']+)["']/i);
    
    const title = titleMatch ? titleMatch[1] : '';
    const description = descriptionMatch ? descriptionMatch[1] : '';
    
    const processedContent = `
Title: ${title}
Description: ${description}
Content: ${textContent.substring(0, 2000)}
`.trim();

    console.log('Scraped content preview:', processedContent.substring(0, 300) + '...');
    return processedContent;
  } catch (error) {
    console.error('Error scraping website:', error);
    throw new Error('Failed to scrape website content');
  }
}

async function generateImageIdeasWithAI(websiteContent: string, url?: string): Promise<any[]> {
  try {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error('Gemini API key not configured');
    }

    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

    const prompt = `
Analyze the following content and generate 3 distinct creative image advertisement concepts.

${url ? `Website URL: ${url}` : 'Brand Information:'}
${websiteContent}

For each concept, provide a JSON object with the following structure:
{
  "summary": "A brief 1-2 sentence description of the image concept",
  "visualDescription": "A detailed description of the visual elements, composition, colors, and style for the image",
  "targetAudience": ["audience segment 1", "audience segment 2", "audience segment 3"],
  "USPs": ["unique selling point 1", "unique selling point 2", "unique selling point 3"],
  "style": "The visual style (e.g., 'photorealistic', 'minimalist', 'modern', 'vintage', 'corporate', 'artistic')"
}

Requirements:
- Base the concepts on the provided content and brand information
- Make each concept visually distinct and creative
- Focus on static image advertisements suitable for social media, web, and print
- Target the specified audience or modern consumers if not specified
- Extract and use real USPs and value propositions from the content
- Include detailed visual descriptions that would work well for AI image generation
- Ensure each concept has a different visual style approach
- Make the visual descriptions specific about composition, lighting, colors, and mood
- Consider different platforms (Instagram, Facebook, LinkedIn, etc.)

Return a JSON array with exactly 3 concepts.
`;

    console.log('Sending prompt to Gemini AI...');
    const result = await model.generateContent(prompt);
    const response = result.response;
    const text = response.text();
    
    console.log('Gemini AI response:', text.substring(0, 300) + '...');

    // Try to parse the JSON response
    try {
      // Extract JSON from the response (in case it's wrapped in markdown)
      const jsonMatch = text.match(/\[[\s\S]*\]/);
      const jsonText = jsonMatch ? jsonMatch[0] : text;
      
      const ideas = JSON.parse(jsonText);
      
      if (!Array.isArray(ideas) || ideas.length === 0) {
        throw new Error('Invalid response format from AI');
      }

      return ideas;
    } catch (parseError) {
      console.error('Failed to parse AI response as JSON:', parseError);
      throw new Error('Failed to parse AI response');
    }
  } catch (error) {
    console.error('Error with AI generation:', error);
    throw error;
  }
}

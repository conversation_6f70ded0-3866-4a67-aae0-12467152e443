import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs/promises';
import path from 'path';

const execAsync = promisify(exec);

export async function POST(request: NextRequest) {
  try {
    const { prompt, negativePrompt, aspectRatio, style } = await request.json();

    if (!prompt) {
      return NextResponse.json(
        { error: 'Prompt is required' },
        { status: 400 }
      );
    }

    console.log('Generating images with:', { prompt, negativePrompt, aspectRatio, style });

    // Map aspect ratio to Imagen format
    const aspectRatioMap: { [key: string]: string } = {
      '1:1': '1:1',
      '16:9': '16:9',
      '9:16': '9:16'
    };

    const imageAspectRatio = aspectRatioMap[aspectRatio] || '1:1';

    // Enhance prompt with style
    const enhancedPrompt = `${prompt}, ${style} style, professional advertising image, high quality, detailed, sharp focus`;

    // Create request JSON for Imagen API
    const requestData = {
      endpoint: "projects/gen-lang-client-0972664248/locations/us-central1/publishers/google/models/imagen-4.0-generate-preview-06-06",
      instances: [
        {
          prompt: enhancedPrompt,
        }
      ],
      parameters: {
        aspectRatio: imageAspectRatio,
        sampleCount: 4,
        negativePrompt: negativePrompt || "",
        enhancePrompt: false,
        personGeneration: "allow_all",
        safetySetting: "block_few",
        addWatermark: true,
        includeRaiReason: true,
        language: "auto"
      }
    };

    // Write request to temporary file
    const tempDir = '/tmp';
    const requestFile = path.join(tempDir, `request-${Date.now()}.json`);
    await fs.writeFile(requestFile, JSON.stringify(requestData, null, 2));

    console.log('Request file created:', requestFile);

    // Set up environment variables
    const PROJECT_ID = "gen-lang-client-0972664248";
    const LOCATION_ID = "us-central1";
    const API_ENDPOINT = "us-central1-aiplatform.googleapis.com";
    const MODEL_ID = "imagen-4.0-generate-preview-06-06";

    // Execute curl command to call Imagen API
    const curlCommand = `curl -X POST \
      -H "Content-Type: application/json" \
      -H "Authorization: Bearer $(gcloud auth print-access-token)" \
      "https://${API_ENDPOINT}/v1/projects/${PROJECT_ID}/locations/${LOCATION_ID}/publishers/google/models/${MODEL_ID}:predict" \
      -d '@${requestFile}'`;

    console.log('Executing curl command...');
    
    try {
      const { stdout, stderr } = await execAsync(curlCommand);
      
      if (stderr) {
        console.error('Curl stderr:', stderr);
      }

      console.log('Imagen API response:', stdout.substring(0, 500) + '...');

      // Parse the response
      const response = JSON.parse(stdout);
      
      if (response.error) {
        throw new Error(`Imagen API error: ${response.error.message || JSON.stringify(response.error)}`);
      }

      // Extract image URLs from the response
      const imageUrls: string[] = [];
      
      if (response.predictions && Array.isArray(response.predictions)) {
        for (const prediction of response.predictions) {
          if (prediction.bytesBase64Encoded) {
            // Convert base64 to data URL
            const dataUrl = `data:image/png;base64,${prediction.bytesBase64Encoded}`;
            imageUrls.push(dataUrl);
          }
        }
      }

      if (imageUrls.length === 0) {
        throw new Error('No images generated in response');
      }

      // Clean up temporary file
      try {
        await fs.unlink(requestFile);
      } catch (cleanupError) {
        console.warn('Failed to clean up request file:', cleanupError);
      }

      console.log(`Successfully generated ${imageUrls.length} images`);

      return NextResponse.json({
        imageUrls,
        metadata: {
          prompt: enhancedPrompt,
          negativePrompt: negativePrompt || '',
          aspectRatio: imageAspectRatio,
          style,
          count: imageUrls.length
        }
      });

    } catch (execError) {
      console.error('Error executing curl command:', execError);
      
      // Clean up temporary file
      try {
        await fs.unlink(requestFile);
      } catch (cleanupError) {
        console.warn('Failed to clean up request file:', cleanupError);
      }

      throw new Error(`Failed to call Imagen API: ${execError}`);
    }

  } catch (error) {
    console.error('Error generating images:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      { error: 'Failed to generate images: ' + errorMessage },
      { status: 500 }
    );
  }
}

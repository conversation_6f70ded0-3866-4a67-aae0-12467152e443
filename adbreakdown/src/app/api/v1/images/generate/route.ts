import { NextRequest, NextResponse } from 'next/server';
import { GoogleAuth } from 'google-auth-library';

export async function POST(request: NextRequest) {
  try {
    const { prompt, negativePrompt, aspectRatio, style } = await request.json();

    if (!prompt) {
      return NextResponse.json(
        { error: 'Prompt is required' },
        { status: 400 }
      );
    }

    console.log('Generating images with:', { prompt, negativePrompt, aspectRatio, style });

    // Map aspect ratio to Imagen format
    const aspectRatioMap: { [key: string]: string } = {
      '1:1': '1:1',
      '16:9': '16:9',
      '9:16': '9:16'
    };

    const imageAspectRatio = aspectRatioMap[aspectRatio] || '1:1';

    // Enhance prompt with style
    const enhancedPrompt = `${prompt}, ${style} style, professional advertising image, high quality, detailed, sharp focus`;

    // Call Imagen API
    const imageUrls = await callImagenAPI(enhancedPrompt, negativePrompt || '', imageAspectRatio);

    console.log(`Successfully generated ${imageUrls.length} images`);

    return NextResponse.json({
      imageUrls,
      metadata: {
        prompt: enhancedPrompt,
        negativePrompt: negativePrompt || '',
        aspectRatio: imageAspectRatio,
        style,
        count: imageUrls.length
      }
    });

    // Write request to temporary file
    const tempDir = '/tmp';
    const requestFile = path.join(tempDir, `request-${Date.now()}.json`);
    await fs.writeFile(requestFile, JSON.stringify(requestData, null, 2));

    console.log('Request file created:', requestFile);

    // Set up environment variables
    const PROJECT_ID = "gen-lang-client-**********";
    const LOCATION_ID = "us-central1";
    const API_ENDPOINT = "us-central1-aiplatform.googleapis.com";
    const MODEL_ID = "imagen-4.0-generate-preview-06-06";

    // Execute curl command to call Imagen API
    const curlCommand = `curl -X POST \
      -H "Content-Type: application/json" \
      -H "Authorization: Bearer $(gcloud auth print-access-token)" \
      "https://${API_ENDPOINT}/v1/projects/${PROJECT_ID}/locations/${LOCATION_ID}/publishers/google/models/${MODEL_ID}:predict" \
      -d '@${requestFile}'`;

    console.log('Executing curl command...');
    
    try {
      const { stdout, stderr } = await execAsync(curlCommand);
      
      if (stderr) {
        console.error('Curl stderr:', stderr);
      }

      console.log('Imagen API response:', stdout.substring(0, 500) + '...');

      // Parse the response
      const response = JSON.parse(stdout);
      
      if (response.error) {
        throw new Error(`Imagen API error: ${response.error.message || JSON.stringify(response.error)}`);
      }

      // Extract image URLs from the response
      const imageUrls: string[] = [];
      
      if (response.predictions && Array.isArray(response.predictions)) {
        for (const prediction of response.predictions) {
          if (prediction.bytesBase64Encoded) {
            // Convert base64 to data URL
            const dataUrl = `data:image/png;base64,${prediction.bytesBase64Encoded}`;
            imageUrls.push(dataUrl);
          }
        }
      }

      if (imageUrls.length === 0) {
        throw new Error('No images generated in response');
      }

      // Clean up temporary file
      try {
        await fs.unlink(requestFile);
      } catch (cleanupError) {
        console.warn('Failed to clean up request file:', cleanupError);
      }

      console.log(`Successfully generated ${imageUrls.length} images`);

      return NextResponse.json({
        imageUrls,
        metadata: {
          prompt: enhancedPrompt,
          negativePrompt: negativePrompt || '',
          aspectRatio: imageAspectRatio,
          style,
          count: imageUrls.length
        }
      });

    } catch (execError) {
      console.error('Error executing curl command:', execError);
      
      // Clean up temporary file
      try {
        await fs.unlink(requestFile);
      } catch (cleanupError) {
        console.warn('Failed to clean up request file:', cleanupError);
      }

      throw new Error(`Failed to call Imagen API: ${execError}`);
    }

  } catch (error) {
    console.error('Error generating images:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      { error: 'Failed to generate images: ' + errorMessage },
      { status: 500 }
    );
  }
}

async function callImagenAPI(prompt: string, negativePrompt: string, aspectRatio: string): Promise<string[]> {
  try {
    // Get Google Cloud credentials
    const serviceAccountKey = JSON.parse(process.env.GOOGLE_CLOUD_SERVICE_ACCOUNT_KEY || '{}');
    const projectId = process.env.VERTEX_AI_PROJECT_ID || 'gen-lang-client-**********';
    const location = process.env.VERTEX_AI_LOCATION || 'us-central1';

    if (!serviceAccountKey.private_key) {
      throw new Error('Missing Google Cloud service account credentials');
    }

    // Initialize Google Auth
    const auth = new GoogleAuth({
      credentials: serviceAccountKey,
      scopes: ['https://www.googleapis.com/auth/cloud-platform']
    });

    const authClient = await auth.getClient();
    const accessToken = await authClient.getAccessToken();

    if (!accessToken.token) {
      throw new Error('Failed to get access token');
    }

    // Prepare the request body for Imagen API
    const requestBody = {
      instances: [
        {
          prompt: prompt,
        }
      ],
      parameters: {
        aspectRatio: aspectRatio,
        sampleCount: 4,
        negativePrompt: negativePrompt,
        enhancePrompt: false,
        personGeneration: "allow_all",
        safetySetting: "block_few",
        addWatermark: true,
        includeRaiReason: true,
        language: "auto"
      }
    };

    console.log('Calling Imagen API with:', {
      prompt: prompt.substring(0, 200) + '...',
      aspectRatio,
      projectId,
      location
    });

    // Call Vertex AI Imagen API
    const response = await fetch(
      `https://${location}-aiplatform.googleapis.com/v1/projects/${projectId}/locations/${location}/publishers/google/models/imagen-4.0-generate-preview-06-06:predict`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Imagen API error:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText
      });
      throw new Error(`Imagen API error: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log('Imagen API response received');

    // Extract image URLs from the response
    const imageUrls: string[] = [];

    if (result.predictions && Array.isArray(result.predictions)) {
      for (const prediction of result.predictions) {
        if (prediction.bytesBase64Encoded) {
          // Convert base64 to data URL
          const dataUrl = `data:image/png;base64,${prediction.bytesBase64Encoded}`;
          imageUrls.push(dataUrl);
        }
      }
    }

    if (imageUrls.length === 0) {
      throw new Error('No images generated in response');
    }

    return imageUrls;

  } catch (error) {
    console.error('Imagen API call failed:', error);
    throw error;
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getJob } from '@/lib/jobStorage';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ jobId: string }> }
) {
  try {
    const { jobId } = await params;
    const job = getJob(jobId);

    if (!job) {
      console.log(`Job ${jobId} not found in storage`);
      return NextResponse.json({ error: 'Job not found' }, { status: 404 });
    }

    const response = {
      status: job.status,
      progress: job.progress,
      message: job.message,
      videoUrl: job.videoUrl,
    };

    console.log(`Status API response for job ${jobId}:`, {
      ...response,
      videoUrl: job.videoUrl ? `${job.videoUrl.substring(0, 100)}...` : null
    });

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching job status:', error);
    return NextResponse.json(
      { error: 'Failed to fetch job status' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { Storage } from '@google-cloud/storage';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    const { path } = await params;
    const filePath = path.join('/');
    
    console.log('Streaming video file:', filePath);
    
    // Initialize Storage with service account credentials
    const serviceAccountKey = JSON.parse(process.env.GOOGLE_CLOUD_SERVICE_ACCOUNT_KEY || '{}');
    const bucketName = 'breakdown-ad-uploads-us-central1'; // Your bucket name
    
    if (!serviceAccountKey.private_key || !serviceAccountKey.client_email) {
      throw new Error('Invalid service account credentials');
    }
    
    const storage = new Storage({
      credentials: serviceAccountKey,
      projectId: process.env.VERTEX_AI_PROJECT_ID,
    });
    
    const bucket = storage.bucket(bucketName);
    const file = bucket.file(filePath);
    
    // Check if file exists
    const [exists] = await file.exists();
    if (!exists) {
      return NextResponse.json({ error: 'Video not found' }, { status: 404 });
    }
    
    // Get file metadata
    const [metadata] = await file.getMetadata();
    
    // Create a read stream
    const stream = file.createReadStream();
    
    // Convert stream to ReadableStream for NextResponse
    const readableStream = new ReadableStream({
      start(controller) {
        let isClosed = false;
        
        stream.on('data', (chunk) => {
          try {
            if (!isClosed) {
              controller.enqueue(chunk);
            }
          } catch (error) {
            console.error('Error enqueueing chunk:', error);
            if (!isClosed) {
              isClosed = true;
              controller.error(error);
            }
          }
        });
        
        stream.on('end', () => {
          try {
            if (!isClosed) {
              isClosed = true;
              controller.close();
            }
          } catch (error) {
            console.error('Error closing controller:', error);
          }
        });
        
        stream.on('error', (error) => {
          console.error('Stream error:', error);
          try {
            if (!isClosed) {
              isClosed = true;
              controller.error(error);
            }
          } catch (controllerError) {
            console.error('Error handling stream error:', controllerError);
          }
        });
      }
    });
    
    return new NextResponse(readableStream, {
      headers: {
        'Content-Type': metadata.contentType || 'video/mp4',
        'Content-Length': metadata.size?.toString() || '',
        'Cache-Control': 'public, max-age=3600',
        'Accept-Ranges': 'bytes',
      },
    });
    
  } catch (error) {
    console.error('Error streaming video:', error);
    return NextResponse.json(
      { error: 'Failed to stream video' },
      { status: 500 }
    );
  }
}
import { NextRequest, NextResponse } from 'next/server';
import { GoogleAuth } from 'google-auth-library';
import { Storage } from '@google-cloud/storage';
import { setJob, updateJob } from '@/lib/jobStorage';

export async function POST(request: NextRequest) {
  try {
    const { script, voiceId, aspectRatio } = await request.json();

    if (!script || !script.scenes || script.scenes.length === 0) {
      return NextResponse.json(
        { error: 'Script with scenes is required' },
        { status: 400 }
      );
    }

    // Generate a unique job ID
    const jobId = `veo_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    
    // Store initial job status
    setJob(jobId, {
      status: 'pending',
      progress: 0,
      message: 'Initializing video generation...',
      createdAt: Date.now(),
      script,
      voiceId,
      aspectRatio,
      videoUrl: null
    });

    console.log('Video generation job created:', {
      jobId,
      scenes: script.scenes.length,
      voiceId,
      aspectRatio
    });

    // Start video generation process asynchronously
    generateVideoAsync(jobId, script, aspectRatio).catch(error => {
      console.error('Video generation failed:', error);
      updateJob(jobId, {
        status: 'failed',
        message: 'Video generation failed: ' + error.message
      });
    });

    return NextResponse.json({
      status: 'pending',
      jobId
    });
  } catch (error) {
    console.error('Error creating video generation job:', error);
    return NextResponse.json(
      { error: 'Failed to create video generation job' },
      { status: 500 }
    );
  }
}

async function generateVideoAsync(jobId: string, script: any, aspectRatio: string) {
  try {
    // Update status to processing
    updateJob(jobId, {
      status: 'processing',
      progress: 10,
      message: 'Converting script to video prompt...'
    });

    // Convert script scenes to a cohesive video prompt
    const videoPrompt = convertScriptToPrompt(script, aspectRatio);
    console.log('Generated video prompt:', videoPrompt);

    // Update progress
    updateJob(jobId, {
      progress: 30,
      message: 'Generating video...'
    });

    // Call Veo 3 API and get the result
    const { videoUrl, operationName } = await callVeoAPI(videoPrompt, aspectRatio);

    // Update to completed
    updateJob(jobId, {
      status: 'completed',
      progress: 100,
      message: 'Video generation completed!',
      videoUrl,
      operationName // Store the operation name for reference
    });

    console.log('Video generation completed:', { 
      jobId, 
      videoUrl: videoUrl ? videoUrl.substring(0, 100) + '...' : null, 
      operationName 
    });
  } catch (error) {
    console.error('Video generation error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    updateJob(jobId, {
      status: 'failed',
      progress: 0,
      message: 'Failed to generate video: ' + errorMessage
    });
  }
}

function convertScriptToPrompt(script: any, aspectRatio: string): string {
  // Convert the scene-by-scene script into a cohesive video prompt
  const scenes = script.scenes;
  
  // Extract key visual elements and narrative
  const visualElements = scenes.map((scene: any) => scene.visualDescription).join('. ');
  const narrative = scenes.map((scene: any) => scene.voiceoverText).join('. ');
  
  // Create a comprehensive prompt for Veo 3
  const prompt = `
Create a ${aspectRatio === '16:9' ? 'landscape' : 'portrait'} format commercial video advertisement:

VISUAL STORY: ${visualElements}

NARRATIVE: ${narrative}

STYLE: Professional commercial advertisement with high production value, smooth transitions between scenes, vibrant colors, modern cinematography. The video should feel premium and engaging, suitable for social media advertising.

DURATION: 8 seconds of dynamic content with seamless scene transitions.
`.trim();

  return prompt;
}

async function callVeoAPI(prompt: string, aspectRatio: string): Promise<{ videoUrl: string; operationName: string }> {
  try {
    // Get Google Cloud credentials
    const serviceAccountKey = JSON.parse(process.env.GOOGLE_CLOUD_SERVICE_ACCOUNT_KEY || '{}');
    const projectId = process.env.VERTEX_AI_PROJECT_ID;
    const location = process.env.VERTEX_AI_LOCATION || 'us-central1';
    
    if (!serviceAccountKey.private_key || !projectId) {
      throw new Error('Missing Google Cloud credentials');
    }

    // Initialize Google Auth
    const auth = new GoogleAuth({
      credentials: serviceAccountKey,
      scopes: ['https://www.googleapis.com/auth/cloud-platform']
    });

    const authClient = await auth.getClient();
    const accessToken = await authClient.getAccessToken();

    if (!accessToken.token) {
      throw new Error('Failed to get access token');
    }

    // Prepare the request body for Veo 3
    const requestBody = {
      instances: [
        {
          prompt: prompt
        }
      ],
      parameters: {
        aspectRatio: aspectRatio,
        resolution: "720p",
        sampleCount: 1,
        storageUri: `gs://${process.env.NEXT_PUBLIC_GCS_BUCKET_NAME}/generated-videos/`
      }
    };

    console.log('Calling Video API with:', {
      prompt: prompt.substring(0, 200) + '...',
      aspectRatio,
      projectId,
      location,
      requestBody // Log the full request body
    });

    // Call Vertex AI Veo API
    const response = await fetch(
      `https://${location}-aiplatform.googleapis.com/v1/projects/${projectId}/locations/${location}/publishers/google/models/veo-3.0-generate-001:predictLongRunning`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Veo API error:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText
      });
      throw new Error(`Veo API error: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log('Veo API response:', result);

    // Extract the operation name for polling
    const operationName = result.name;
    if (!operationName) {
      throw new Error('No operation name returned from Veo API');
    }

    console.log('Starting to poll operation:', operationName);
    
    // Poll the operation until completion
    const videoUrl = await pollVeoOperation(operationName, accessToken.token!, location);
    return { videoUrl, operationName };
    
  } catch (error) {
    console.error('Veo API call failed:', error);
    throw error;
  }
}

async function pollVeoOperation(operationName: string, accessToken: string, location: string): Promise<string> {
  const maxAttempts = 40; // Maximum 10 minutes (40 attempts * 15 seconds)
  const pollInterval = 15000; // 15 seconds - more reasonable for video generation

  // Extract project ID and model ID from the operation name
  const operationParts = operationName.split('/');
  const projectId = operationParts[1];
  const modelId = operationParts[7]; // Corrected: "veo-3.0-generate-001"

  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    try {
      console.log(`Polling attempt ${attempt + 1}/${maxAttempts} for operation: ${operationName}`);

      // Validate access token before making request
      if (!accessToken || accessToken.length < 10) {
        throw new Error('Invalid or missing access token');
      }

      // Use the fetchPredictOperation endpoint as per Veo documentation
      const pollUrl = `https://${location}-aiplatform.googleapis.com/v1/projects/${projectId}/locations/${location}/publishers/google/models/${modelId}:fetchPredictOperation`;
      console.log('Polling URL:', pollUrl);

      const pollResponse = await fetch(
        pollUrl,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            operationName: operationName
          })
        }
      );

      if (!pollResponse.ok) {
        const errorText = await pollResponse.text();
        console.error('Operation polling error:', errorText);
        throw new Error(`Failed to poll operation: ${pollResponse.status}`);
      }

      const operationResult = await pollResponse.json();
      console.log(`Operation status:`, {
        done: operationResult.done,
        state: operationResult.metadata?.state || 'unknown'
      });

      // Check if operation is complete
      if (operationResult.done) {
        if (operationResult.error) {
          console.error('Operation failed:', operationResult.error);
          throw new Error(`Video generation failed: ${operationResult.error.message}`);
        }

        // Extract the video URL from the response
        const response = operationResult.response;
        console.log('Full operation response:', JSON.stringify(response, null, 2));

        if (response && response.videos && response.videos.length > 0) {
          // Veo API returns videos array with gcsUri or bytesBase64Encoded
          const video = response.videos[0];

          let videoUrl = null;
          if (video.gcsUri) {
            // Convert gs:// URI to a streaming URL through our API
            const gcsUri = video.gcsUri;
            console.log('Processing GCS URI:', gcsUri);
            
            // Extract bucket and file path from gs://bucket-name/path/to/file.mp4
            const gcsMatch = gcsUri.match(/^gs:\/\/([^\/]+)\/(.+)$/);
            if (!gcsMatch) {
              throw new Error(`Invalid GCS URI format: ${gcsUri}`);
            }
            
            const bucketName = gcsMatch[1];
            const fileName = gcsMatch[2];
            
            console.log('Extracted:', { bucketName, fileName });

            // Create a streaming URL through our API instead of signed URL
            videoUrl = `/api/v1/videos/stream/${fileName}`;
            console.log('Generated streaming URL:', videoUrl);
          } else if (video.bytesBase64Encoded) {
            // If video is returned as bytes, we'd need to save it to GCS first
            console.log('Video returned as bytes, need to save to storage');
            // For now, return a placeholder - in production, save bytes to GCS
            videoUrl = 'data:video/mp4;base64,' + video.bytesBase64Encoded;
          }

          if (videoUrl) {
            console.log('Video generation completed! URL:', videoUrl);
            console.log('Returning videoUrl from pollVeoOperation:', videoUrl);
            console.log(`Polling completed successfully for operation: ${operationName}`);
            return videoUrl;
          } else {
            console.error('No video URL found in video object:', video);
            throw new Error('Video URL not found in completed operation');
          }
        } else {
          console.error('No videos in response:', response);
          throw new Error('No videos in completed operation');
        }
      }

      // Wait before next poll
      if (attempt < maxAttempts - 1) {
        console.log(`Operation not complete, waiting ${pollInterval/1000} seconds...`);
        await new Promise(resolve => setTimeout(resolve, pollInterval));
      }
    } catch (error) {
      console.error(`Polling attempt ${attempt + 1} failed:`, error);
      if (attempt === maxAttempts - 1) {
        throw new Error(`Polling failed after ${maxAttempts} attempts: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, pollInterval));
    }
  }

  throw new Error('Video generation timed out after 10 minutes');
}

// Job storage is now handled by the shared jobStorage module
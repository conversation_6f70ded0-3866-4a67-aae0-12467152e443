import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

interface ManualInfoData {
  brandName: string;
  category: string;
  subCategory: string;
  targetAudience: string;
  market: string;
  usp: string;
  videoDescription: string;
}

export async function POST(request: NextRequest) {
  try {
    const { url, manualData, videoDescription } = await request.json();

    if (!url) {
      return NextResponse.json(
        { error: 'URL is required' },
        { status: 400 }
      );
    }

    console.log('Generating ideas for URL:', url);
    console.log('Manual data provided:', !!manualData);
    console.log('Video description provided:', !!videoDescription);
    if (manualData) {
      console.log('Manual data keys:', Object.keys(manualData));
    }

    let websiteContent: string;
    
    // Check if this is manual data (mock URL), real URL, or hybrid
    if (manualData && url.includes('manual-brand-info.local')) {
      // Pure manual data
      console.log('Using manual brand data:', manualData);
      websiteContent = formatManualDataAsWebContent(manualData);
    } else if (manualData && !url.includes('manual-brand-info.local')) {
      // Hybrid: Real URL + manual override data
      console.log('Using hybrid approach: URL + manual data override');
      console.log('Manual data keys:', Object.keys(manualData));
      console.log('Video description from top level:', !!videoDescription);
      const scrapedContent = await scrapeWebsite(url);
      websiteContent = combineUrlAndManualData(scrapedContent, manualData, url);
      console.log('Combined website content length:', websiteContent.length);
    } else {
      // Pure URL
      websiteContent = await scrapeWebsite(url);
    }
    
    // Generate ideas using Gemini AI
    console.log('Final videoDescription being passed to AI:', videoDescription);
    console.log('Website content length:', websiteContent.length);
    console.log('Website content preview:', websiteContent.substring(0, 500) + '...');
    const ideas = await generateIdeasWithAI(websiteContent, url, videoDescription);

    return NextResponse.json(ideas);
  } catch (error) {
    console.error('Error generating ideas:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      { error: 'Failed to generate ideas: ' + errorMessage },
      { status: 500 }
    );
  }
}

function formatManualDataAsWebContent(data: ManualInfoData): string {
  return `
Title: ${data.brandName} - ${data.category}${data.subCategory ? ` | ${data.subCategory}` : ''}
Description: ${data.brandName} offers ${data.category.toLowerCase()}${data.subCategory ? ` specializing in ${data.subCategory.toLowerCase()}` : ''} targeting ${data.targetAudience}${data.market ? ` in ${data.market}` : ''}.
${data.usp ? `Unique Selling Proposition: ${data.usp}` : ''}
${data.videoDescription ? `Video Requirements: ${data.videoDescription}` : ''}

Brand Information:
- Brand Name: ${data.brandName}
- Category: ${data.category}
${data.subCategory ? `- Sub Category: ${data.subCategory}` : ''}
- Target Audience: ${data.targetAudience}
${data.market ? `- Market: ${data.market}` : ''}
${data.usp ? `- Key Value Proposition: ${data.usp}` : ''}
${data.videoDescription ? `- Video Description: ${data.videoDescription}` : ''}
`.trim();
}

function combineUrlAndManualData(scrapedContent: string, manualData: Partial<ManualInfoData>, url: string): string {
  const manualOverrides = [];
  if (manualData.brandName) manualOverrides.push(`- Brand Name: ${manualData.brandName}`);
  if (manualData.category) manualOverrides.push(`- Category: ${manualData.category}`);
  if (manualData.subCategory) manualOverrides.push(`- Sub Category: ${manualData.subCategory}`);
  if (manualData.targetAudience) manualOverrides.push(`- Target Audience: ${manualData.targetAudience}`);
  if (manualData.market) manualOverrides.push(`- Market: ${manualData.market}`);
  if (manualData.usp) manualOverrides.push(`- Unique Selling Proposition: ${manualData.usp}`);

  return `
WEBSITE CONTENT (from ${url}):
${scrapedContent}

${manualOverrides.length > 0 ? `MANUAL BRAND SPECIFICATIONS (PRIORITY - use these over website data):
${manualOverrides.join('\n')}

AUGMENTATION INSTRUCTIONS: Use the website content for product/service details and general information, but PRIORITIZE and incorporate the manual brand specifications above. These are the client's specific requirements that override any conflicting information from the website.` : ''}
`.trim();
}

async function scrapeWebsite(url: string): Promise<string> {
  try {
    console.log('Scraping website:', url);
    
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch website: ${response.status}`);
    }

    const html = await response.text();
    
    // Extract text content from HTML (simple approach)
    const textContent = html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove scripts
      .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '') // Remove styles
      .replace(/<[^>]+>/g, ' ') // Remove HTML tags
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();

    // Extract key sections (title, meta description, etc.)
    const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
    const descriptionMatch = html.match(/<meta[^>]*name=["']description["'][^>]*content=["']([^"']+)["']/i);
    
    const title = titleMatch ? titleMatch[1] : '';
    const description = descriptionMatch ? descriptionMatch[1] : '';
    
    const processedContent = `
Title: ${title}
Description: ${description}
Content: ${textContent.substring(0, 2000)}
`.trim();

    console.log('Scraped content preview:', processedContent.substring(0, 300) + '...');
    return processedContent;
  } catch (error) {
    console.error('Error scraping website:', error);
    throw new Error('Failed to scrape website content');
  }
}

async function generateIdeasWithAI(websiteContent: string, url: string, videoDescription?: string): Promise<any[]> {
  try {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error('Gemini API key not configured');
    }

    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

    const prompt = `
Analyze the following content and generate 2 distinct creative video advertisement concepts.

${url.includes('manual-brand-info.local') ? 'Brand Information:' : `Website URL: ${url}`}
${websiteContent}

${videoDescription ? `
EXPLICIT VIDEO INSTRUCTIONS (CRITICAL - must be incorporated):
${videoDescription}

These video instructions are MANDATORY requirements that must be reflected in both concepts. They define the style, tone, focus, and approach for the video ads.` : ''}

For each concept, provide a JSON object with the following structure:
{
  "summary": "A detailed 2-3 sentence narrative concept for a video ad",
  "hook": "A compelling one-liner that captures attention",
  "targetAudience": ["audience segment 1", "audience segment 2", "audience segment 3"],
  "USPs": ["unique selling point 1", "unique selling point 2", "unique selling point 3", "unique selling point 4"]
}

Requirements:
- Base the concepts on the provided content and brand information
- Make each concept distinct and creative while following the same brand guidelines
- Focus on visual storytelling suitable for 8-10 second video ads
- Target the specified audience or modern consumers if not specified
- Extract and use real USPs and value propositions from the content
- If product information is available, focus on the products/services offered
- If this is a service, focus on the benefits and solutions provided
${videoDescription ? `- CRITICAL: Every concept MUST incorporate the video instructions: "${videoDescription}". This overrides any conflicting guidance.` : ''}
- If manual brand specifications are provided, they take PRIORITY over any website content
- Ensure concepts are aligned with the specified target audience and brand positioning

Return a JSON array with exactly 2 concepts.
`;

    console.log('Sending prompt to Gemini AI...');
    const result = await model.generateContent(prompt);
    const response = result.response;
    const text = response.text();
    
    console.log('Gemini AI response:', text.substring(0, 300) + '...');

    // Try to parse the JSON response
    try {
      // Extract JSON from the response (in case it's wrapped in markdown)
      const jsonMatch = text.match(/\[[\s\S]*\]/);
      const jsonText = jsonMatch ? jsonMatch[0] : text;
      
      const ideas = JSON.parse(jsonText);
      
      if (!Array.isArray(ideas) || ideas.length === 0) {
        throw new Error('Invalid response format from AI');
      }

      return ideas;
    } catch (parseError) {
      console.error('Failed to parse AI response as JSON:', parseError);
      throw new Error('Failed to parse AI response');
    }
  } catch (error) {
    console.error('Error with AI generation:', error);
    throw error;
  }
}
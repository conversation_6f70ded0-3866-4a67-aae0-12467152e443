import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    console.log('🔧 Setting up generated_assets table...');

    const supabase = createServerSupabaseClient();

    // Create the generated_assets table
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS public.generated_assets (
        id uuid NOT NULL DEFAULT uuid_generate_v4(),
        user_id uuid NOT NULL,
        
        -- Asset Information
        asset_type text NOT NULL CHECK (asset_type IN ('image', 'video', 'audio', 'text')),
        asset_name text NOT NULL,
        asset_url text NOT NULL,
        file_size integer,
        mime_type text,
        
        -- Generation Context
        generation_type text NOT NULL CHECK (generation_type IN ('imagen', 'veo', 'text_generation', 'audio_generation')),
        prompt text,
        negative_prompt text,
        generation_parameters jsonb,
        
        -- Metadata
        thumbnail_url text,
        duration_seconds integer,
        dimensions jsonb,
        
        -- Organization
        tags text[] DEFAULT '{}',
        is_favorite boolean DEFAULT false,
        folder_path text,
        
        -- Status and Timestamps
        status text DEFAULT 'completed' CHECK (status IN ('generating', 'completed', 'failed', 'deleted')),
        created_at timestamp with time zone DEFAULT now(),
        updated_at timestamp with time zone DEFAULT now(),
        
        CONSTRAINT generated_assets_pkey PRIMARY KEY (id),
        CONSTRAINT generated_assets_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE
      );
    `;

    // Execute the table creation
    const { error: tableError } = await supabase.rpc('exec', { 
      sql: createTableSQL 
    });

    if (tableError) {
      console.error('Table creation error:', tableError);
      // Try alternative approach
      const { error: altError } = await supabase
        .from('generated_assets')
        .select('id')
        .limit(1);

      if (altError && altError.message.includes('does not exist')) {
        return NextResponse.json({
          error: 'Failed to create table. Please create it manually in Supabase dashboard.',
          sql: createTableSQL
        }, { status: 500 });
      }
    }

    // Create indexes
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_generated_assets_user_id ON public.generated_assets(user_id);',
      'CREATE INDEX IF NOT EXISTS idx_generated_assets_type ON public.generated_assets(asset_type);',
      'CREATE INDEX IF NOT EXISTS idx_generated_assets_generation_type ON public.generated_assets(generation_type);',
      'CREATE INDEX IF NOT EXISTS idx_generated_assets_created_at ON public.generated_assets(created_at DESC);',
      'CREATE INDEX IF NOT EXISTS idx_generated_assets_status ON public.generated_assets(status);',
      'CREATE INDEX IF NOT EXISTS idx_generated_assets_is_favorite ON public.generated_assets(is_favorite);'
    ];

    for (const indexSQL of indexes) {
      await supabase.rpc('exec', { sql: indexSQL });
    }

    // Enable RLS
    const rlsSQL = `
      ALTER TABLE public.generated_assets ENABLE ROW LEVEL SECURITY;
      
      DROP POLICY IF EXISTS "Users can only access their own generated assets" ON public.generated_assets;
      
      CREATE POLICY "Users can only access their own generated assets" 
      ON public.generated_assets FOR ALL USING (true);
    `;

    await supabase.rpc('exec', { sql: rlsSQL });

    // Test the table by checking if it exists
    const { data: testData, error: testError } = await supabase
      .from('generated_assets')
      .select('id')
      .limit(1);

    if (testError && !testError.message.includes('0 rows')) {
      console.error('Table test error:', testError);
      return NextResponse.json({
        error: 'Table created but not accessible',
        details: testError.message
      }, { status: 500 });
    }

    console.log('✅ Generated assets table setup completed');

    return NextResponse.json({
      success: true,
      message: 'Generated assets table created successfully',
      tableExists: true
    });

  } catch (error) {
    console.error('Setup error:', error);
    return NextResponse.json({
      error: 'Failed to setup database',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

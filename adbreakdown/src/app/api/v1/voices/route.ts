import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Mock voice options
    const voices = [
      {
        id: 'voice_emma_prof',
        name: '<PERSON> (Professional)',
        gender: 'female',
        accent: 'American',
        previewUrl: 'https://example.com/voice-previews/emma.mp3'
      },
      {
        id: 'voice_james_friendly',
        name: '<PERSON> (<PERSON>)',
        gender: 'male',
        accent: 'British',
        previewUrl: 'https://example.com/voice-previews/james.mp3'
      },
      {
        id: 'voice_sofia_energetic',
        name: '<PERSON> (Energetic)',
        gender: 'female',
        accent: 'Spanish',
        previewUrl: 'https://example.com/voice-previews/sofia.mp3'
      },
      {
        id: 'voice_david_auth',
        name: '<PERSON> (Authoritative)',
        gender: 'male',
        accent: 'Australian',
        previewUrl: 'https://example.com/voice-previews/david.mp3'
      },
      {
        id: 'voice_maya_warm',
        name: '<PERSON> (<PERSON>)',
        gender: 'female',
        accent: 'Indian',
        previewUrl: 'https://example.com/voice-previews/maya.mp3'
      }
    ];

    return NextResponse.json(voices);
  } catch (error) {
    console.error('Error fetching voices:', error);
    return NextResponse.json(
      { error: 'Failed to fetch voices' },
      { status: 500 }
    );
  }
}
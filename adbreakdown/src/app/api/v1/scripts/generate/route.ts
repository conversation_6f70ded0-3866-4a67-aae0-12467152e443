import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('Script generation request received:', body);
    
    const { idea, duration } = body;

    if (!idea || !duration) {
      console.error('Missing required fields:', { idea: !!idea, duration: !!duration });
      return NextResponse.json(
        { error: 'Idea and duration are required', received: { idea: !!idea, duration: !!duration } },
        { status: 400 }
      );
    }

    console.log('Generating script with AI...');
    
    // Generate script using AI
    const script = await generateScriptWithAI(idea, duration);
    
    return NextResponse.json(script);
  } catch (error) {
    console.error('Error generating script:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      { error: 'Failed to generate script: ' + errorMessage },
      { status: 500 }
    );
  }
}

async function generateScriptWithAI(idea: any, duration: number): Promise<any> {
  try {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error('Gemini API key not configured');
    }

    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

    // Calculate number of scenes based on duration
    const numScenes = Math.ceil(duration / 3); // Roughly 3 seconds per scene

    const prompt = `
Create a detailed video script for a ${duration}-second commercial advertisement based on the following concept:

CONCEPT SUMMARY: ${idea.summary}
HOOK: ${idea.hook}
TARGET AUDIENCE: ${idea.targetAudience.join(', ')}
KEY SELLING POINTS: ${idea.USPs.join(', ')}

Generate a script with exactly ${numScenes} scenes. Each scene should be approximately ${Math.floor(duration / numScenes)} seconds long.

Return a JSON object with this exact structure:
{
  "scenes": [
    {
      "sceneNumber": 1,
      "visualDescription": "Detailed description of what the viewer sees in this scene - camera angles, setting, actions, visual effects",
      "voiceoverText": "The exact words spoken during this scene - keep it concise and impactful"
    }
  ]
}

Requirements:
- Each scene's visualDescription should be detailed enough for video generation AI
- Include specific visual elements, settings, and actions
- Voiceover should be natural and conversational
- Build narrative momentum across scenes
- End with a strong call-to-action or brand reinforcement
- Focus on visual storytelling that works without sound
- Make it suitable for social media advertising (engaging from the first second)
- Each scene should flow naturally into the next

Return only valid JSON with no additional text or markdown formatting.
`;

    console.log(`Generating ${numScenes} scenes for ${duration}-second video...`);
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    console.log('Gemini AI script response:', text.substring(0, 300) + '...');

    // Try to parse the JSON response
    try {
      // Extract JSON from the response (in case it's wrapped in markdown)
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      const jsonText = jsonMatch ? jsonMatch[0] : text;
      
      const script = JSON.parse(jsonText);
      
      if (!script.scenes || !Array.isArray(script.scenes) || script.scenes.length === 0) {
        throw new Error('Invalid script format from AI');
      }

      // Ensure scene numbers are correct
      script.scenes = script.scenes.map((scene: any, index: number) => ({
        ...scene,
        sceneNumber: index + 1
      }));

      console.log(`Generated script with ${script.scenes.length} scenes`);
      return script;
    } catch (parseError) {
      console.error('Failed to parse AI script response as JSON:', parseError);
      throw new Error('Failed to parse AI script response');
    }
  } catch (error) {
    console.error('Error with AI script generation:', error);
    throw error;
  }
}
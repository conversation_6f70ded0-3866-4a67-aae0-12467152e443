import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServerSupabaseClient } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user from database
    const supabase = createServerSupabaseClient();
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const assetType = searchParams.get('type'); // 'image', 'video', 'audio', 'text'
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const search = searchParams.get('search');
    const favorites = searchParams.get('favorites') === 'true';

    // Build query
    let query = supabase
      .from('generated_assets')
      .select('*')
      .eq('user_id', user.id)
      .eq('status', 'completed')
      .order('created_at', { ascending: false });

    // Apply filters
    if (assetType && assetType !== 'all') {
      query = query.eq('asset_type', assetType);
    }

    if (favorites) {
      query = query.eq('is_favorite', true);
    }

    if (search) {
      // Search in asset name and prompt
      query = query.or(`asset_name.ilike.%${search}%,prompt.ilike.%${search}%`);
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: assets, error: assetsError } = await query;

    if (assetsError) {
      console.error('Error fetching assets:', assetsError);
      return NextResponse.json(
        { error: 'Failed to fetch assets' },
        { status: 500 }
      );
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('generated_assets')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)
      .eq('status', 'completed');

    if (assetType && assetType !== 'all') {
      countQuery = countQuery.eq('asset_type', assetType);
    }

    if (favorites) {
      countQuery = countQuery.eq('is_favorite', true);
    }

    if (search) {
      countQuery = countQuery.or(`asset_name.ilike.%${search}%,prompt.ilike.%${search}%`);
    }

    const { count, error: countError } = await countQuery;

    if (countError) {
      console.error('Error getting assets count:', countError);
    }

    return NextResponse.json({
      assets: assets || [],
      pagination: {
        total: count || 0,
        limit,
        offset,
        hasMore: (count || 0) > offset + limit
      }
    });

  } catch (error) {
    console.error('Error in assets API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { assetId, updates } = await request.json();

    if (!assetId) {
      return NextResponse.json(
        { error: 'Asset ID is required' },
        { status: 400 }
      );
    }

    // Get user from database
    const supabase = createServerSupabaseClient();
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Update the asset (only allow certain fields to be updated)
    const allowedUpdates = {
      asset_name: updates.asset_name,
      is_favorite: updates.is_favorite,
      tags: updates.tags,
      folder_path: updates.folder_path
    };

    // Remove undefined values
    const cleanUpdates = Object.fromEntries(
      Object.entries(allowedUpdates).filter(([_, value]) => value !== undefined)
    );

    const { data: updatedAsset, error: updateError } = await supabase
      .from('generated_assets')
      .update(cleanUpdates)
      .eq('id', assetId)
      .eq('user_id', user.id) // Ensure user can only update their own assets
      .select()
      .single();

    if (updateError) {
      console.error('Error updating asset:', updateError);
      return NextResponse.json(
        { error: 'Failed to update asset' },
        { status: 500 }
      );
    }

    if (!updatedAsset) {
      return NextResponse.json(
        { error: 'Asset not found or access denied' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      asset: updatedAsset,
      message: 'Asset updated successfully'
    });

  } catch (error) {
    console.error('Error in assets PATCH API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const assetId = searchParams.get('id');

    if (!assetId) {
      return NextResponse.json(
        { error: 'Asset ID is required' },
        { status: 400 }
      );
    }

    // Get user from database
    const supabase = createServerSupabaseClient();
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Soft delete the asset (mark as deleted instead of actually deleting)
    const { data: deletedAsset, error: deleteError } = await supabase
      .from('generated_assets')
      .update({ status: 'deleted' })
      .eq('id', assetId)
      .eq('user_id', user.id) // Ensure user can only delete their own assets
      .select()
      .single();

    if (deleteError) {
      console.error('Error deleting asset:', deleteError);
      return NextResponse.json(
        { error: 'Failed to delete asset' },
        { status: 500 }
      );
    }

    if (!deletedAsset) {
      return NextResponse.json(
        { error: 'Asset not found or access denied' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: 'Asset deleted successfully'
    });

  } catch (error) {
    console.error('Error in assets DELETE API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

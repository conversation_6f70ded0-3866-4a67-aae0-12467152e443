import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { createServerSupabaseClient } from '@/lib/supabase';
import { auth } from '@clerk/nextjs/server';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { contentType, platform, tone, length, brandInfo } = await request.json();

    if (!contentType || !platform || !tone || !length) {
      return NextResponse.json(
        { error: 'Content type, platform, tone, and length are required' },
        { status: 400 }
      );
    }

    console.log('Generating text content with:', { contentType, platform, tone, length });

    // Get user from database
    const supabase = createServerSupabaseClient();
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Generate content using AI
    const content = await generateTextContentWithAI(contentType, platform, tone, length, brandInfo);

    console.log(`✅ Successfully generated ${contentType} content`);

    // Save to database
    try {
      const { error: saveError } = await supabase
        .from('generated_assets')
        .insert({
          user_id: user.id,
          asset_type: 'text',
          asset_name: `${contentType.replace('-', ' ').toUpperCase()} - ${new Date().toLocaleDateString()}`,
          asset_url: `data:text/plain;base64,${Buffer.from(content).toString('base64')}`, // Store as data URL
          generation_type: 'text_generation',
          prompt: `Generate ${contentType} content for ${platform} with ${tone} tone and ${length} length`,
          generation_parameters: {
            contentType,
            platform,
            tone,
            length,
            brandInfo
          },
          mime_type: 'text/plain',
          status: 'completed'
        });

      if (saveError) {
        console.error('Failed to save text asset to database:', saveError);
      } else {
        console.log('✅ Saved text asset to database');
      }
    } catch (dbError) {
      console.error('Database save error:', dbError);
    }

    return NextResponse.json({
      content,
      metadata: {
        contentType,
        platform,
        tone,
        length,
        wordCount: content.split(' ').length,
        characterCount: content.length
      }
    });

  } catch (error) {
    console.error('Error generating text content:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      { error: 'Failed to generate text content: ' + errorMessage },
      { status: 500 }
    );
  }
}

async function generateTextContentWithAI(
  contentType: string, 
  platform: string, 
  tone: string, 
  length: string, 
  brandInfo: string
): Promise<string> {
  try {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error('Gemini API key not configured');
    }

    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({ 
      model: 'gemini-1.5-flash',
      generationConfig: {
        temperature: 0.8,
        topP: 0.95,
        maxOutputTokens: 2048,
      }
    });

    const prompt = generatePromptForContentType(contentType, platform, tone, length, brandInfo);

    console.log('Sending content generation prompt to Gemini AI...');
    const result = await model.generateContent(prompt);
    const response = result.response;
    const content = response.text();
    
    console.log('Gemini AI content generated:', content.substring(0, 200) + '...');

    return content.trim();
  } catch (error) {
    console.error('Error with AI content generation:', error);
    throw error;
  }
}

function generatePromptForContentType(
  contentType: string, 
  platform: string, 
  tone: string, 
  length: string, 
  brandInfo: string
): string {
  const baseContext = `
Brand Information: ${brandInfo}
Platform: ${platform}
Tone: ${tone}
Length: ${length}
`;

  switch (contentType) {
    case 'tv-ad-30s':
      return `${baseContext}

Create a 30-second TV commercial script with the following requirements:
- Professional broadcast format with scene descriptions
- Clear voiceover text and visual cues
- Strong opening hook within first 3 seconds
- Compelling call-to-action in final 5 seconds
- ${tone} tone throughout
- Include timing markers (e.g., [0:00-0:05])
- Format as a proper TV script with SCENE, VOICEOVER, and ACTION elements

The script should be engaging, memorable, and drive viewers to take action.`;

    case 'reel-30s':
      return `${baseContext}

Create a 30-second social media reel script optimized for Instagram/TikTok:
- Hook viewers in the first 2 seconds
- Include visual cues and text overlay suggestions
- ${tone} tone that resonates with social media audiences
- Strong call-to-action for engagement
- Include hashtag suggestions
- Format with timing and visual direction
- Consider trending social media formats and styles

The script should be shareable, engaging, and optimized for mobile viewing.`;

    case 'bumper-5s':
      return `${baseContext}

Create a 5-second bumper ad script:
- Ultra-concise messaging (maximum impact in minimal time)
- Immediate brand recognition
- Single, powerful message
- ${tone} tone
- Strong visual impact suggestion
- Clear call-to-action
- Format with precise timing

Every word must count in this ultra-short format.`;

    case 'performance-ad':
      return `${baseContext}

Create high-converting performance ad copy:
- Attention-grabbing headline
- Clear value proposition
- Benefit-focused body text
- Strong call-to-action
- ${tone} tone
- Include multiple variations (headline, body, CTA)
- Focus on conversion optimization
- Address pain points and solutions

The copy should be designed to drive immediate action and conversions.`;

    case 'google-ad':
      return `${baseContext}

Create Google Ads copy with the following components:
- 3 Headlines (30 characters each)
- 2 Descriptions (90 characters each)
- ${tone} tone
- Include relevant keywords naturally
- Clear value proposition
- Strong call-to-action
- Comply with Google Ads policies

Format as:
Headlines:
1. [Headline 1]
2. [Headline 2]
3. [Headline 3]

Descriptions:
1. [Description 1]
2. [Description 2]

The copy should be optimized for search intent and click-through rates.`;

    case 'meta-ad':
      return `${baseContext}

Create Meta (Facebook/Instagram) ad copy:
- Compelling primary text (125 characters)
- Attention-grabbing headline (40 characters)
- Clear call-to-action
- ${tone} tone
- Social media optimized language
- Include emoji suggestions where appropriate
- Focus on engagement and social proof

Format as:
Primary Text: [Main ad copy]
Headline: [Short headline]
Call-to-Action: [CTA button text]

The copy should be optimized for social media engagement and conversions.`;

    default:
      return `${baseContext}

Create compelling ad copy for ${contentType}:
- ${tone} tone
- ${length} format
- Platform-optimized for ${platform}
- Clear value proposition
- Strong call-to-action
- Engaging and persuasive content

Generate high-quality copy that drives results.`;
  }
}

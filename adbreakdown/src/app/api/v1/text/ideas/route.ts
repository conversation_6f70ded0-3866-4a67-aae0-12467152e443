import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

interface ManualInfoData {
  brandName: string;
  category: string;
  subCategory: string;
  targetAudience: string;
  market: string;
  usp: string;
  videoDescription: string;
}

export async function POST(request: NextRequest) {
  try {
    const { url, brandName, category, subCategory, targetAudience, market, usp, videoDescription } = await request.json();

    console.log('Generating text content ideas for:', { url, brandName, category });

    let websiteContent: string;
    
    // Check if we have manual data or URL
    if (brandName && category && targetAudience && usp) {
      // Manual data provided
      const manualData: ManualInfoData = {
        brandName,
        category,
        subCategory: subCategory || '',
        targetAudience,
        market: market || '',
        usp,
        videoDescription: videoDescription || ''
      };
      websiteContent = formatManualDataAsWebContent(manualData);
    } else if (url) {
      // URL provided
      websiteContent = await scrapeWebsite(url);
    } else {
      return NextResponse.json(
        { error: 'Either URL or complete brand information (brandName, category, targetAudience, usp) is required' },
        { status: 400 }
      );
    }
    
    // Generate text content ideas using Gemini AI
    const ideas = await generateTextIdeasWithAI(websiteContent, url);

    return NextResponse.json(ideas);
  } catch (error) {
    console.error('Error generating text ideas:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      { error: 'Failed to generate text ideas: ' + errorMessage },
      { status: 500 }
    );
  }
}

function formatManualDataAsWebContent(data: ManualInfoData): string {
  return `
Title: ${data.brandName} - ${data.category}${data.subCategory ? ` | ${data.subCategory}` : ''}
Description: ${data.brandName} offers ${data.category.toLowerCase()}${data.subCategory ? ` specializing in ${data.subCategory.toLowerCase()}` : ''} targeting ${data.targetAudience}${data.market ? ` in ${data.market}` : ''}.
${data.usp ? `Unique Selling Proposition: ${data.usp}` : ''}
${data.videoDescription ? `Additional Context: ${data.videoDescription}` : ''}

Brand Information:
- Brand Name: ${data.brandName}
- Category: ${data.category}
${data.subCategory ? `- Sub Category: ${data.subCategory}` : ''}
- Target Audience: ${data.targetAudience}
${data.market ? `- Market: ${data.market}` : ''}
${data.usp ? `- Key Value Proposition: ${data.usp}` : ''}
${data.videoDescription ? `- Additional Context: ${data.videoDescription}` : ''}
`.trim();
}

async function scrapeWebsite(url: string): Promise<string> {
  try {
    console.log('Scraping website:', url);
    
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch website: ${response.status}`);
    }

    const html = await response.text();
    
    // Extract text content from HTML
    const textContent = html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
      .replace(/<[^>]+>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();

    // Extract key sections
    const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
    const descriptionMatch = html.match(/<meta[^>]*name=["']description["'][^>]*content=["']([^"']+)["']/i);
    
    const title = titleMatch ? titleMatch[1] : '';
    const description = descriptionMatch ? descriptionMatch[1] : '';
    
    const processedContent = `
Title: ${title}
Description: ${description}
Content: ${textContent.substring(0, 2000)}
`.trim();

    console.log('Scraped content preview:', processedContent.substring(0, 300) + '...');
    return processedContent;
  } catch (error) {
    console.error('Error scraping website:', error);
    throw new Error('Failed to scrape website content');
  }
}

async function generateTextIdeasWithAI(websiteContent: string, url?: string): Promise<any[]> {
  try {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error('Gemini API key not configured');
    }

    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

    const prompt = `
Analyze the following content and generate content ideas for different types of text-based advertisements.

${url ? `Website URL: ${url}` : 'Brand Information:'}
${websiteContent}

Generate ideas for these content types:
1. TV Commercial (30s script)
2. Social Media Reel (30s script)
3. Bumper Ad (5s script)
4. Performance Ad Copy
5. Google Ads Copy
6. Meta Ads Copy

For each content type, provide a JSON object with the following structure:
{
  "summary": "Brief description of the content approach",
  "contentType": "tv-ad-30s" | "reel-30s" | "bumper-5s" | "performance-ad" | "google-ad" | "meta-ad",
  "targetAudience": ["audience segment 1", "audience segment 2", "audience segment 3"],
  "keyMessages": ["key message 1", "key message 2", "key message 3"],
  "callToAction": "Primary call-to-action for this content type"
}

Requirements:
- Base the ideas on the provided content and brand information
- Make each content type distinct and optimized for its platform
- Focus on compelling messaging that drives action
- Extract real value propositions from the content
- Ensure each idea has a clear target audience and call-to-action
- Consider platform-specific best practices (TV vs social vs search ads)

Return a JSON array with exactly 6 content ideas (one for each type).
`;

    console.log('Sending prompt to Gemini AI...');
    const result = await model.generateContent(prompt);
    const response = result.response;
    const text = response.text();
    
    console.log('Gemini AI response:', text.substring(0, 300) + '...');

    // Try to parse the JSON response
    try {
      // Extract JSON from the response (in case it's wrapped in markdown)
      const jsonMatch = text.match(/\[[\s\S]*\]/);
      const jsonText = jsonMatch ? jsonMatch[0] : text;
      
      const ideas = JSON.parse(jsonText);
      
      if (!Array.isArray(ideas) || ideas.length === 0) {
        throw new Error('Invalid response format from AI');
      }

      return ideas;
    } catch (parseError) {
      console.error('Failed to parse AI response as JSON:', parseError);
      throw new Error('Failed to parse AI response');
    }
  } catch (error) {
    console.error('Error with AI generation:', error);
    throw error;
  }
}

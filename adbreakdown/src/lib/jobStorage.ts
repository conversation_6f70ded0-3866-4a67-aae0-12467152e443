import fs from 'fs';
import path from 'path';

// Shared job storage for video generation
// In production, replace this with Redis or a database
export const jobStorage = new Map<string, any>();

// Environment detection
const isProduction = process.env.NODE_ENV === 'production' || process.env.VERCEL === '1';
const enableFilePersistence = !isProduction && process.env.ENABLE_FILE_PERSISTENCE !== 'false';

// File-based persistence for development only
const STORAGE_FILE = path.join(process.cwd(), '.tmp', 'jobs.json');

console.log(`Job storage initialized: production=${isProduction}, filePersistence=${enableFilePersistence}`);

// Ensure storage directory exists (development only)
function ensureStorageDir() {
  if (!enableFilePersistence) return;
  
  try {
    const dir = path.dirname(STORAGE_FILE);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  } catch (error) {
    console.warn('Could not create storage directory:', error);
  }
}

let isLoaded = false;

// Load jobs from file on startup (development only)
function loadJobs() {
  if (isLoaded || !enableFilePersistence) {
    return; // Don't reload if already loaded or in production
  }
  
  try {
    ensureStorageDir();
    if (fs.existsSync(STORAGE_FILE)) {
      const data = fs.readFileSync(STORAGE_FILE, 'utf8');
      const jobs = JSON.parse(data);
      Object.entries(jobs).forEach(([jobId, job]) => {
        jobStorage.set(jobId, job);
      });
      console.log(`Loaded ${Object.keys(jobs).length} jobs from persistent storage`);
    }
    isLoaded = true;
  } catch (error) {
    console.warn('Error loading jobs from storage (continuing with memory-only):', error);
    isLoaded = true; // Mark as loaded to prevent retries
  }
}

// Save jobs to file (development only)
function saveJobs() {
  if (!enableFilePersistence) {
    return; // Skip file operations in production
  }
  
  try {
    ensureStorageDir();
    const jobs = Object.fromEntries(jobStorage.entries());
    fs.writeFileSync(STORAGE_FILE, JSON.stringify(jobs, null, 2));
  } catch (error) {
    console.warn('Error saving jobs to storage (continuing with memory-only):', error);
  }
}

// Load jobs on module initialization
loadJobs();

export interface JobStatus {
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  message: string;
  createdAt: number;
  script?: any;
  voiceId?: string;
  aspectRatio?: string;
  videoUrl?: string | null;
  operationName?: string;
}

export function getJob(jobId: string): JobStatus | undefined {
  // Always reload from disk to ensure we have the latest data
  loadJobsFromDisk();
  
  const job = jobStorage.get(jobId);
  if (!job) {
    console.warn(`Job ${jobId} not found. Available jobs:`, Array.from(jobStorage.keys()));
    console.warn(`Total jobs in storage: ${jobStorage.size}`);
  } else {
    console.log(`Found job ${jobId}:`, {
      status: job.status,
      progress: job.progress,
      message: job.message,
      hasVideoUrl: !!job.videoUrl,
      videoUrlPreview: job.videoUrl ? job.videoUrl.substring(0, 50) + '...' : null
    });
  }
  return job;
}

// Separate function to force reload from disk (development only)
function loadJobsFromDisk() {
  if (!enableFilePersistence) {
    return; // Skip in production - jobs are only in memory
  }
  
  try {
    ensureStorageDir();
    if (fs.existsSync(STORAGE_FILE)) {
      const data = fs.readFileSync(STORAGE_FILE, 'utf8');
      const jobs = JSON.parse(data);
      
      // Clear current storage and reload
      jobStorage.clear();
      Object.entries(jobs).forEach(([jobId, job]) => {
        jobStorage.set(jobId, job);
      });
    }
  } catch (error) {
    console.warn('Error loading jobs from disk (continuing with memory-only):', error);
  }
}

export function setJob(jobId: string, status: JobStatus): void {
  jobStorage.set(jobId, status);
  saveJobs();
  console.log(`Job ${jobId} created and saved to persistent storage`);
}

export function updateJob(jobId: string, updates: Partial<JobStatus>): void {
  const existing = jobStorage.get(jobId);
  if (existing) {
    const updated = { ...existing, ...updates };
    jobStorage.set(jobId, updated);
    saveJobs();
    
    // Log when videoUrl is being updated
    if (updates.videoUrl !== undefined) {
      console.log(`Job ${jobId} videoUrl updated and saved:`, {
        hasVideoUrl: !!updates.videoUrl,
        videoUrlPreview: updates.videoUrl ? updates.videoUrl.substring(0, 100) + '...' : null
      });
    }
  } else {
    console.warn(`Attempted to update non-existent job: ${jobId}`);
  }
}

export function deleteJob(jobId: string): void {
  jobStorage.delete(jobId);
  saveJobs();
  console.log(`Job ${jobId} deleted from persistent storage`);
}

import { Storage } from '@google-cloud/storage'

let storage: Storage

// Initialize Google Cloud Storage
try {
  const gcsCredentials = JSON.parse(process.env.GOOGLE_CLOUD_SERVICE_ACCOUNT_KEY || '{}')
  
  storage = new Storage({
    projectId: process.env.GOOGLE_PROJECT_ID,
    credentials: gcsCredentials,
  })
  
  console.log('✅ GCS Storage utility initialized successfully')
} catch (error) {
  console.error('❌ Failed to initialize GCS storage utility:', error)
  // Fallback initialization
  storage = new Storage({
    projectId: process.env.GOOGLE_PROJECT_ID,
  })
}

const bucketName = process.env.NEXT_PUBLIC_GCS_BUCKET_NAME || 'breakdown-ad-uploads-us-central1'

export class GCSService {
  static async uploadBase64Image(base64Data: string, fileName: string, contentType: string = 'image/png'): Promise<string> {
    try {
      if (!bucketName) {
        throw new Error('NEXT_PUBLIC_GCS_BUCKET_NAME is not configured')
      }

      console.log(`📤 Uploading image to GCS: ${fileName}`)

      // Remove data URL prefix if present (data:image/png;base64,)
      const base64Content = base64Data.replace(/^data:image\/[a-z]+;base64,/, '')

      // Convert base64 to buffer
      const buffer = Buffer.from(base64Content, 'base64')

      const gcsFile = storage.bucket(bucketName).file(fileName)

      // Upload the file
      await gcsFile.save(buffer, {
        metadata: {
          contentType,
          cacheControl: 'public, max-age=31536000', // 1 year cache
        },
        // Note: public: true is not compatible with uniform bucket-level access
        // The bucket should be configured with public access at the bucket level
      })

      // Generate a signed URL for access (valid for 7 days)
      const [signedUrl] = await gcsFile.getSignedUrl({
        action: 'read',
        expires: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7 days
      })

      console.log(`✅ Successfully uploaded image to GCS with signed URL`)
      return signedUrl

    } catch (error: any) {
      console.error('❌ Failed to upload image to GCS:', {
        fileName,
        error: error.message,
        code: error.code
      })
      throw error
    }
  }

  static async deleteFile(gcsUri: string): Promise<boolean> {
    try {
      if (!bucketName) {
        throw new Error('NEXT_PUBLIC_GCS_BUCKET_NAME is not configured')
      }

      // Extract filename from GCS URI (gs://bucket-name/filename)
      const uriPattern = /^gs:\/\/([^\/]+)\/(.+)$/
      const match = gcsUri.match(uriPattern)
      
      if (!match) {
        throw new Error(`Invalid GCS URI format: ${gcsUri}`)
      }

      const [, bucket, fileName] = match
      
      if (bucket !== bucketName) {
        throw new Error(`URI bucket ${bucket} doesn't match configured bucket ${bucketName}`)
      }

      console.log(`🗑️ Deleting file from GCS: ${fileName}`)
      
      const gcsFile = storage.bucket(bucketName).file(fileName)
      
      // Check if file exists first
      const [exists] = await gcsFile.exists()
      if (!exists) {
        console.log(`⚠️ File ${fileName} doesn't exist in GCS, skipping deletion`)
        return true // Consider non-existent files as successfully "deleted"
      }

      // Delete the file
      await gcsFile.delete()
      
      console.log(`✅ Successfully deleted file from GCS: ${fileName}`)
      return true
      
    } catch (error: any) {
      console.error('❌ Failed to delete file from GCS:', {
        gcsUri,
        error: error.message,
        code: error.code
      })
      
      // Don't throw error - log it but don't fail the analysis deletion
      // if GCS cleanup fails
      return false
    }
  }

  static async fileExists(gcsUri: string): Promise<boolean> {
    try {
      if (!bucketName) {
        return false
      }

      const uriPattern = /^gs:\/\/([^\/]+)\/(.+)$/
      const match = gcsUri.match(uriPattern)
      
      if (!match) {
        return false
      }

      const [, bucket, fileName] = match
      
      if (bucket !== bucketName) {
        return false
      }

      const gcsFile = storage.bucket(bucketName).file(fileName)
      const [exists] = await gcsFile.exists()
      
      return exists
      
    } catch (error) {
      console.error('❌ Error checking if GCS file exists:', error)
      return false
    }
  }

  static extractFileNameFromUri(gcsUri: string): string | null {
    try {
      const uriPattern = /^gs:\/\/([^\/]+)\/(.+)$/
      const match = gcsUri.match(uriPattern)
      return match ? match[2] : null
    } catch (error) {
      return null
    }
  }
}
export interface VideoIdea {
  summary: string;
  hook: string;
  targetAudience: string[];
  USPs: string[];
}

export interface Scene {
  sceneNumber: number;
  visualDescription: string;
  voiceoverText: string;
}

export interface VideoScript {
  scenes: Scene[];
}

export interface Voice {
  id: string;
  name: string;
  gender: 'male' | 'female';
  accent: string;
  previewUrl?: string;
}

export type AspectRatio = '9:16' | '16:9' | '1:1';

export type VideoGenerationStep = 
  | 'init'
  | 'url-input'
  | 'generating-ideas'
  | 'idea-selection'
  | 'duration-selection'
  | 'voice-selection'
  | 'aspect-ratio-selection'
  | 'script-generation'
  | 'script-editing'
  | 'final-review'
  | 'video-generation'
  | 'completed';

export type GenerationStatus = 'idle' | 'pending' | 'processing' | 'completed' | 'failed';

export interface VideoGenerationState {
  step: VideoGenerationStep;
  url: string;
  ideas: VideoIdea[];
  selectedIdea: VideoIdea | null;
  duration: number | null;
  voice: Voice | null;
  aspectRatio: AspectRatio | null;
  script: VideoScript | null;
  jobId: string | null;
  videoUrl: string | null;
  status: GenerationStatus;
}

export interface ChatMessage {
  id: string;
  text: string;
  sender: 'user' | 'bot';
  component?: React.ReactNode;
  timestamp: Date;
}

// Image generation types
export interface ImageIdea {
  summary: string;
  visualDescription: string;
  targetAudience: string[];
  USPs: string[];
  style: string;
}

export type ImageGenerationStep =
  | 'init'
  | 'url-input'
  | 'generating-ideas'
  | 'idea-selection'
  | 'style-selection'
  | 'aspect-ratio-selection'
  | 'prompt-editing'
  | 'final-review'
  | 'image-generation'
  | 'completed';

export interface ImageGenerationState {
  step: ImageGenerationStep;
  url: string;
  ideas: ImageIdea[];
  selectedIdea: ImageIdea | null;
  style: string | null;
  aspectRatio: AspectRatio | null;
  prompt: string | null;
  negativePrompt: string | null;
  jobId: string | null;
  imageUrls: string[] | null;
  status: GenerationStatus;
}

// Text generation types
export interface TextIdea {
  summary: string;
  contentType: ContentType;
  targetAudience: string[];
  keyMessages: string[];
  callToAction: string;
}

export type ContentType =
  | 'tv-ad-30s'
  | 'reel-30s'
  | 'bumper-5s'
  | 'performance-ad'
  | 'google-ad'
  | 'meta-ad';

export type Platform =
  | 'tv'
  | 'instagram'
  | 'facebook'
  | 'youtube'
  | 'google'
  | 'meta'
  | 'tiktok';

export type ToneOfVoice =
  | 'professional'
  | 'casual'
  | 'friendly'
  | 'urgent'
  | 'humorous'
  | 'authoritative'
  | 'emotional'
  | 'conversational';

export type ContentLength =
  | '5s'
  | '15s'
  | '30s'
  | '60s'
  | 'short'
  | 'medium'
  | 'long';

export type TextGenerationStep =
  | 'init'
  | 'url-input'
  | 'generating-ideas'
  | 'idea-selection'
  | 'content-customization'
  | 'tone-selection'
  | 'final-review'
  | 'text-generation'
  | 'completed';

export interface TextGenerationState {
  step: TextGenerationStep;
  url: string;
  ideas: TextIdea[];
  selectedIdea: TextIdea | null;
  contentType: ContentType | null;
  platform: Platform | null;
  tone: ToneOfVoice | null;
  length: ContentLength | null;
  generatedContent: string | null;
  jobId: string | null;
  status: GenerationStatus;
}

// Audio generation types
export interface AudioIdea {
  summary: string;
  audioType: AudioType;
  targetAudience: string[];
  keyElements: string[];
  mood: AudioMood;
}

export type AudioType =
  | 'radio-ad'
  | 'jingle'
  | 'background-music'
  | 'voiceover'
  | 'podcast-intro'
  | 'sound-effect';

export type VoiceType =
  | 'male-professional'
  | 'female-professional'
  | 'male-casual'
  | 'female-casual'
  | 'male-energetic'
  | 'female-energetic'
  | 'narrator'
  | 'child'
  | 'elderly';

export type AudioMood =
  | 'upbeat'
  | 'calm'
  | 'energetic'
  | 'professional'
  | 'playful'
  | 'dramatic'
  | 'mysterious'
  | 'romantic'
  | 'corporate';

export type AudioDuration =
  | '5s'
  | '10s'
  | '15s'
  | '30s'
  | '60s'
  | '2min'
  | '5min';

export type AudioGenerationStep =
  | 'init'
  | 'url-input'
  | 'generating-ideas'
  | 'idea-selection'
  | 'audio-customization'
  | 'voice-selection'
  | 'final-review'
  | 'audio-generation'
  | 'completed';

export interface AudioGenerationState {
  step: AudioGenerationStep;
  url: string;
  ideas: AudioIdea[];
  selectedIdea: AudioIdea | null;
  audioType: AudioType | null;
  voice: VoiceType | null;
  mood: AudioMood | null;
  duration: AudioDuration | null;
  generatedAudio: string | null;
  jobId: string | null;
  status: GenerationStatus;
}
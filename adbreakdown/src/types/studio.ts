export interface VideoIdea {
  summary: string;
  hook: string;
  targetAudience: string[];
  USPs: string[];
}

export interface Scene {
  sceneNumber: number;
  visualDescription: string;
  voiceoverText: string;
}

export interface VideoScript {
  scenes: Scene[];
}

export interface Voice {
  id: string;
  name: string;
  gender: 'male' | 'female';
  accent: string;
  previewUrl?: string;
}

export type AspectRatio = '9:16' | '16:9' | '1:1';

export type VideoGenerationStep = 
  | 'init'
  | 'url-input'
  | 'generating-ideas'
  | 'idea-selection'
  | 'duration-selection'
  | 'voice-selection'
  | 'aspect-ratio-selection'
  | 'script-generation'
  | 'script-editing'
  | 'final-review'
  | 'video-generation'
  | 'completed';

export type GenerationStatus = 'idle' | 'pending' | 'processing' | 'completed' | 'failed';

export interface VideoGenerationState {
  step: VideoGenerationStep;
  url: string;
  ideas: VideoIdea[];
  selectedIdea: VideoIdea | null;
  duration: number | null;
  voice: Voice | null;
  aspectRatio: AspectRatio | null;
  script: VideoScript | null;
  jobId: string | null;
  videoUrl: string | null;
  status: GenerationStatus;
}

export interface ChatMessage {
  id: string;
  text: string;
  sender: 'user' | 'bot';
  component?: React.ReactNode;
  timestamp: Date;
}
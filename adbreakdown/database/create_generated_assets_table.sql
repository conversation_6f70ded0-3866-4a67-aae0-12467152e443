-- Create generated_assets table for storing AI-generated content
-- Run this in the Supabase SQL Editor

-- Create generated_assets table
CREATE TABLE IF NOT EXISTS public.generated_assets (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  
  -- Asset Information
  asset_type text NOT NULL CHECK (asset_type IN ('image', 'video', 'audio', 'text')),
  asset_name text NOT NULL,
  asset_url text NOT NULL,
  file_size integer,
  mime_type text,
  
  -- Generation Context
  generation_type text NOT NULL CHECK (generation_type IN ('imagen', 'veo', 'text_generation', 'audio_generation')),
  prompt text,
  negative_prompt text,
  generation_parameters jsonb,
  
  -- Metadata
  thumbnail_url text,
  duration_seconds integer,
  dimensions jsonb,
  
  -- Organization
  tags text[] DEFAULT '{}',
  is_favorite boolean DEFAULT false,
  folder_path text,
  
  -- Status and Timestamps
  status text DEFAULT 'completed' CHECK (status IN ('generating', 'completed', 'failed', 'deleted')),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT generated_assets_pkey PRIMARY KEY (id),
  CONSTRAINT generated_assets_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_generated_assets_user_id ON public.generated_assets(user_id);
CREATE INDEX IF NOT EXISTS idx_generated_assets_type ON public.generated_assets(asset_type);
CREATE INDEX IF NOT EXISTS idx_generated_assets_generation_type ON public.generated_assets(generation_type);
CREATE INDEX IF NOT EXISTS idx_generated_assets_created_at ON public.generated_assets(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_generated_assets_status ON public.generated_assets(status);
CREATE INDEX IF NOT EXISTS idx_generated_assets_is_favorite ON public.generated_assets(is_favorite);

-- Add updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_generated_assets_updated_at 
BEFORE UPDATE ON public.generated_assets
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS (Row Level Security)
ALTER TABLE public.generated_assets ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for users to only see their own generated assets
DROP POLICY IF EXISTS "Users can only access their own generated assets" ON public.generated_assets;

CREATE POLICY "Users can only access their own generated assets" ON public.generated_assets
FOR ALL USING (true); -- Service role bypasses RLS, actual auth happens in API layer

-- Grant permissions
GRANT ALL ON public.generated_assets TO authenticated;
GRANT ALL ON public.generated_assets TO service_role;

-- Verify table creation
SELECT 
    table_name, 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'generated_assets' 
ORDER BY ordinal_position;

-- V23_create_generated_assets_table.sql
-- Migration to create generated_assets table for storing AI-generated content

-- Create generated_assets table
CREATE TABLE public.generated_assets (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  
  -- Asset Information
  asset_type text NOT NULL CHECK (asset_type IN ('image', 'video', 'audio', 'text')),
  asset_name text NOT NULL,
  asset_url text NOT NULL, -- GCS URL or signed URL
  file_size integer,
  mime_type text,
  
  -- Generation Context
  generation_type text NOT NULL CHECK (generation_type IN ('imagen', 'veo', 'text_generation', 'audio_generation')),
  prompt text, -- The prompt used to generate the asset
  negative_prompt text, -- For image generation
  generation_parameters jsonb, -- Store generation settings (aspect ratio, style, etc.)
  
  -- Metadata
  thumbnail_url text, -- For videos and large images
  duration_seconds integer, -- For video/audio assets
  dimensions jsonb, -- Store width/height for images/videos
  
  -- Organization
  tags text[], -- User-defined tags for organization
  is_favorite boolean DEFAULT false,
  folder_path text, -- For future folder organization
  
  -- Status and Timestamps
  status text DEFAULT 'completed' CHECK (status IN ('generating', 'completed', 'failed', 'deleted')),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT generated_assets_pkey PRIMARY KEY (id),
  CONSTRAINT generated_assets_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE
);

-- Add indexes for performance
CREATE INDEX idx_generated_assets_user_id ON public.generated_assets(user_id);
CREATE INDEX idx_generated_assets_type ON public.generated_assets(asset_type);
CREATE INDEX idx_generated_assets_generation_type ON public.generated_assets(generation_type);
CREATE INDEX idx_generated_assets_created_at ON public.generated_assets(created_at DESC);
CREATE INDEX idx_generated_assets_status ON public.generated_assets(status);
CREATE INDEX idx_generated_assets_is_favorite ON public.generated_assets(is_favorite);

-- Add updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_generated_assets_updated_at 
BEFORE UPDATE ON public.generated_assets
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS (Row Level Security)
ALTER TABLE public.generated_assets ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for users to only see their own generated assets
CREATE POLICY "Users can only access their own generated assets" ON public.generated_assets
FOR ALL USING (true); -- Service role bypasses RLS, actual auth happens in API layer

-- Grant permissions
GRANT ALL ON public.generated_assets TO authenticated;
GRANT ALL ON public.generated_assets TO service_role;

-- Insert some sample data for testing (optional)
-- This will be removed in production
INSERT INTO public.generated_assets (
  user_id, 
  asset_type, 
  asset_name, 
  asset_url, 
  generation_type, 
  prompt,
  generation_parameters,
  mime_type
) VALUES (
  (SELECT id FROM public.users LIMIT 1), -- Use first user for testing
  'image',
  'Sample Generated Image',
  'https://storage.googleapis.com/breakdown-ad-uploads-us-central1/generated-images/sample.png',
  'imagen',
  'A beautiful landscape with mountains and lakes',
  '{"aspectRatio": "1:1", "style": "photorealistic", "sampleCount": 4}',
  'image/png'
) ON CONFLICT DO NOTHING; -- Prevent errors if no users exist

-- Verify table creation
SELECT 
    table_name, 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'generated_assets' 
ORDER BY ordinal_position;
